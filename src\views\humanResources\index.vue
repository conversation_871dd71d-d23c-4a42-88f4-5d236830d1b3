<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="中文人力资源" name="first"></el-tab-pane>
      <el-tab-pane label="其他语言人力资源" name="second"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="90px">
      <el-form-item label="职务名称" prop="positionTitle">
        <el-input v-model="queryParams.positionTitle" placeholder="请输入职务名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="工作地点" prop="workAddress">
        <el-input v-model="queryParams.workAddress" placeholder="请输入工作地点" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="发布日期">
        <el-date-picker v-model="daterangePublicDate" size="small" style="width: 240px" value-format="yyyy-MM-dd"
          type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item label="是否已禁用" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="语言类型" prop="languageType">
        <el-select filterable v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
          <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['yq:humanResources:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['yq:humanResources:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['yq:humanResources:remove']">删除
        </el-button>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--          v-hasPermi="['yq:humanResources:export']"-->
      <!--        >导出</el-button>-->
      <!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="humanResourcesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column width="200" label="职务名称" align="center" prop="positionTitle" />
      <el-table-column width="200" label="cms菜单" align="center" prop="menuName" />
      <el-table-column width="200" label="简介" align="center" prop="description" />
      <el-table-column label="人数" align="center" prop="peopleNumber" />
      <el-table-column width="200" label="工作地点" align="center" prop="workAddress" />
      <el-table-column label="发布日期" align="center" prop="publicDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.publicDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否已禁用" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum" />
      <el-table-column width="200" label="语言类型" align="center" prop="languageType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType" />
        </template>
      </el-table-column>
      <el-table-column fixed="right" width="120" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:humanResources:edit']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCopy(scope.row)"
            v-hasPermi="['yq:humanResources:edit']">复制
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['yq:humanResources:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改人力资源对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false">
      <!-- 语言标签页和按钮 -->
      <div style="position: relative; margin-bottom: 20px;">
        <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
          @tab-click="handleLanguageTabClick">
          <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label"
            :name="lang.value"></el-tab-pane>
        </el-tabs>

        <div
          style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px; z-index: 1;">
          <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
            @click="handleAutoTranslate">自动翻译其他语言</el-button>
          <el-button size="mini" type="warning" icon="el-icon-delete"
            @click="handleClearOtherLanguages">清空其他语言</el-button>
        </div>
      </div>

      <el-form ref="form" :model="multiLanguageForms[activeLanguageTab] || form" :rules="rules" label-width="100px">
        <el-form-item label="职务名称" prop="positionTitle">
          <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).positionTitle" placeholder="请输入职务名称"
            show-word-limit />
        </el-form-item>
        <el-form-item label="cms菜单" prop="cmsMenuId">
          <treeselect v-model="(multiLanguageForms[activeLanguageTab] || form).cmsMenuId" :options="humanResourcesMenus"
            :default-expand-level="1" :normalizer="normalizer" placeholder="请选择cms菜单">
            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
              :class="labelClassName">
              {{ node.label }} ： {{ node.raw.language }}
              <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
            </label>
          </treeselect>
        </el-form-item>
        <!-- 隐藏语言类型表单项 -->
        <el-form-item label="简介" prop="description">
          <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).description" placeholder="请输入简介"
            show-word-limit />
        </el-form-item>
        <el-form-item label="人数" prop="peopleNumber">
          <el-input-number v-model="(multiLanguageForms[activeLanguageTab] || form).peopleNumber" :min="0" :max="999999"
            :precision="0" placeholder="请输入人数" />
        </el-form-item>
        <el-form-item label="工作地点" prop="workAddress">
          <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).workAddress" placeholder="请输入工作地点"
            show-word-limit />
        </el-form-item>
        <el-form-item label="发布日期" prop="publicDate">
          <el-date-picker clearable size="small" v-model="(multiLanguageForms[activeLanguageTab] || form).publicDate"
            type="date" value-format="yyyy-MM-dd" placeholder="选择发布日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="外部链接" prop="grainWeight">
          <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).externalLinks" placeholder="请输入外部链接" />
        </el-form-item>

        <el-form-item label="详情内容">
          <Tinymce ref="editor" v-model="(multiLanguageForms[activeLanguageTab] || form).content" :height="400" />
        </el-form-item>
        <el-form-item label="是否禁用" prop="status">
          <el-select v-model="(multiLanguageForms[activeLanguageTab] || form).status" placeholder="请选择状态">
            <el-option v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="(multiLanguageForms[activeLanguageTab] || form).orderNum" :min="0" :max="999999"
            :precision="0" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input :rows="3" type="textarea" maxlength="255"
            v-model="(multiLanguageForms[activeLanguageTab] || form).remark" placeholder="请输入备注" show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listHumanResources,
  getHumanResources,
  delHumanResources,
  addHumanResources,
  updateHumanResources,
  autoTranslateHumanResourcesData,
  batchSaveMultiLanguage,
  getByRelationGroup
} from '@/api/yq/humanResources'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cmsMenuTreeselect, getCmsMenu } from '@/api/yq/cmsMenu'

export default {
  name: 'HumanResources',
  components: {
    Treeselect
  },
  dicts: ['status_yes_no', 'i18N_language'],
  data() {
    return {
      activeName: 'first',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      cmsMenu: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 人力资源表格数据
      humanResourcesList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangePublicDate: [],
      //人力资源菜单
      humanResourcesMenus: [],
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前激活的语言标签页
      activeLanguageTab: 'zh_CN',
      // 翻译状态
      translating: false,
      // 提交状态
      submitting: false,
      // 当前编辑的关系组ID
      currentRelationGroupId: null,
      // 是否已经翻译过(用于判断是否需要二次确认)
      hasTranslated: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        positionTitle: null,
        cmsMenuId: null,
        workAddress: null,
        publicDate: null,
        status: null,
        languageType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        positionTitle: [
          { required: true, message: '职务名称不能为空', trigger: 'blur' }
        ],
        cmsMenuId: [
          { required: true, message: 'cms菜单 不能为空', trigger: 'change' }
        ],
        peopleNumber: [
          { required: true, message: '人数 不能为空', trigger: 'change' }
        ],
        workAddress: [
          { required: true, message: '工作地点 不能为空', trigger: 'change' }
        ],
        publicDate: [
          { required: true, message: '发布日期 不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    // 等待字典数据加载完成后再初始化
    this.$nextTick(() => {
      this.waitForDictAndInitialize()
    })
  },
  computed: {
    // 从字典中获取支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  watch: {
    'form.cmsMenuId'(val) {
      if (val) {
        getCmsMenu(val).then(response => {
          let info = response.data
          if (info) {
            this.form.languageType = info.languageType
          }
        })
      } else {
        this.form.languageType = null
      }
    }
  },
  methods: {
    // 等待字典数据加载并初始化
    waitForDictAndInitialize() {
      // 检查字典数据是否已加载
      if (this.dict && this.dict.type && this.dict.type.i18N_language && this.dict.type.status_yes_no) {
        this.initializeDefaultLanguage()
      } else {
        // 如果字典数据还未加载，延迟重试
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 50)
      }
    },
    // 初始化默认语言
    initializeDefaultLanguage() {
      const languages = this.dict.type.i18N_language
      // 优先选择中文，如果没有则选择第一个
      const zhCN = languages.find(lang => lang.value === 'zh_CN')
      this.activeLanguageTab = zhCN ? 'zh_CN' : languages[0].value
      this.initializeMultiLanguageForms()
    },
    // 初始化多语言表单
    initializeMultiLanguageForms() {
      const languages = this.dict.type.i18N_language
      languages.forEach(lang => {
        if (!this.multiLanguageForms[lang.value]) {
          this.$set(this.multiLanguageForms, lang.value, {
            id: null,
            positionTitle: null,
            cmsMenuId: null,
            description: null,
            peopleNumber: null,
            workAddress: null,
            publicDate: null,
            content: null,
            status: '0',
            orderNum: null,
            languageType: lang.value,
            createBy: null,
            createTime: null,
            updateTime: null,
            updateBy: null,
            delFlag: null,
            externalLinks: null,
            remark: null,
            relationGroupId: this.currentRelationGroupId
          })
        }
      })
    },
    // 加载多语言数据
    loadMultiLanguageData(relationGroupId) {
      if (!relationGroupId) {
        return
      }

      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []
        // 清空现有数据
        this.multiLanguageForms = {}
        this.initializeMultiLanguageForms()

        // 填充已有的多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.$set(this.multiLanguageForms, item.languageType, { ...item })
          }
        })

        // 设置默认激活的tab为中文
        this.activeLanguageTab = 'zh_CN'
      }).catch(error => {
        this.$modal.msgError('加载多语言数据失败：' + (error.msg || error.message))
      })
    },

    handleClick(tab, event) {
      if (tab.index == 0) {
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    /** 转换新闻类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        name: node.label,
        children: node.children
      }
    },

    /**
     * 获取人力资源菜单
     */
    getHumanResourcesMenus() {
      cmsMenuTreeselect().then(res => {
        this.humanResourcesMenus = res.data
      })
    },

    /** 查询人力资源列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangePublicDate && '' != this.daterangePublicDate) {
        this.queryParams.params['beginPublicDate'] = this.daterangePublicDate[0]
        this.queryParams.params['endPublicDate'] = this.daterangePublicDate[1]
      }
      listHumanResources(this.queryParams).then(response => {
        this.humanResourcesList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.cmsMenu = {}
      this.form = {
        id: null,
        positionTitle: null,
        cmsMenuId: null,
        description: null,
        peopleNumber: null,
        workAddress: null,
        publicDate: null,
        content: null,
        status: null,
        orderNum: null,
        languageType: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        externalLinks: null,
        remark: null
      }
      // 重置多语言表单数据
      this.multiLanguageForms = {}
      this.currentRelationGroupId = null
      // 重置翻译状态
      this.hasTranslated = false
      // 确保字典数据可用后再初始化
      if (this.dict && this.dict.type && this.dict.type.i18N_language) {
        this.initializeMultiLanguageForms()
        this.initializeDefaultLanguage()
      }

      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangePublicDate = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getHumanResourcesMenus()
      this.open = true
      this.title = '添加人力资源'

      // 设置默认语言类型
      this.activeLanguageTab = 'zh_CN'
      if (this.multiLanguageForms[this.activeLanguageTab]) {
        this.multiLanguageForms[this.activeLanguageTab].languageType = this.activeLanguageTab
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getHumanResourcesMenus()
      const id = row.id || this.ids
      getHumanResources(id).then(response => {
        const currentData = response.data

        // 检查是否有多语言关联关系
        if (currentData.relationGroupId) {
          this.currentRelationGroupId = currentData.relationGroupId
          // 获取所有语言版本
          this.loadMultiLanguageData(currentData.relationGroupId)
        } else {
          // 没有关联关系，只加载当前语言版本
          this.form = currentData
          this.multiLanguageForms[currentData.languageType] = { ...this.form }
          this.activeLanguageTab = currentData.languageType
        }

        this.open = true
        this.title = '修改人力资源'
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      this.getHumanResourcesMenus()
      const id = row.id || this.ids
      getHumanResources(id).then(response => {
        const sourceData = response.data

        // 检查是否有多语言关联关系
        if (sourceData.relationGroupId) {
          // 加载多语言数据
          getByRelationGroup(sourceData.relationGroupId).then(relationResponse => {
            const multiLanguageData = relationResponse.data || []

            // 为每个语言创建副本并清除ID和关系组ID
            multiLanguageData.forEach(item => {
              if (this.multiLanguageForms[item.languageType]) {
                const copyData = { ...item }
                copyData.id = null
                copyData.relationGroupId = null
                this.$set(this.multiLanguageForms, item.languageType, copyData)
              }
            })

            this.activeLanguageTab = sourceData.languageType || 'zh_CN'
          })
        } else {
          // 只复制当前语言版本
          this.form = { ...sourceData, id: null }
          this.multiLanguageForms[sourceData.languageType] = { ...this.form }
          this.activeLanguageTab = sourceData.languageType
        }

        this.open = true
        this.title = '复制人力资源'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 准备要提交的数据
          const dataToSave = {};

          // 添加有数据的表单
          this.supportedLanguages.forEach(lang => {
            const formData = this.multiLanguageForms[lang.value];
            if (formData && (formData.positionTitle || formData.id)) {
              dataToSave[lang.value] = { ...formData };
            }
          });

          if (Object.keys(dataToSave).length === 0) {
            this.$modal.msgError('请至少填写一个语言版本的数据');
            return;
          }

          this.submitting = true;
          // 批量保存多语言版本
          batchSaveMultiLanguage(dataToSave).then(response => {
            this.$modal.msgSuccess('保存成功');
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError('保存失败：' + (error.msg || error.message));
          }).finally(() => {
            this.submitting = false;
          });
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('注意删除人力资源，将同步删除此数据关联的其他语言版本，是否确认删除人力资源编号为"' + ids + '"的数据项？').then(function () {
        return delHumanResources(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/humanResources/export', {
        ...this.queryParams
      }, `humanResources_${new Date().getTime()}.xlsx`)
    },
    /**
     * 切换语言标签页
     */
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
      // 更新表单数据
      const currentForm = this.multiLanguageForms[this.activeLanguageTab]
      if (currentForm) {
        this.form = currentForm
      }
    },
    /**
     * 自动翻译其他语言
     */
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.positionTitle) {
        this.$modal.msgError('请先填写当前语言版本的职务名称')
        return
      }

      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm)
        }).catch(() => { })
      } else {
        this.executeTranslation(sourceForm)
      }
    },

    /** 执行翻译 */
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateHumanResourcesData(sourceForm).then(response => {
        const translatedData = response.data || {}

        // 更新多语言表单数据
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab && this.multiLanguageForms[lang]) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang].id
            const originalRelationGroupId = this.multiLanguageForms[lang].relationGroupId

            // 更新表单数据
            this.$set(this.multiLanguageForms, lang, {
              ...translatedData[lang],
              id: originalId,
              relationGroupId: originalRelationGroupId || this.currentRelationGroupId,
              cmsMenuId: this.multiLanguageForms[this.activeLanguageTab].cmsMenuId // 保持cms菜单ID一致
            })
          }
        })

        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败: ' + error)
      }).finally(() => {
        this.translating = false
      })
    },

    /**
     * 清空其他语言
     */
    handleClearOtherLanguages() {
      this.$modal.confirm('确定要清空其他语言的数据吗？').then(() => {
        const currentLanguageCode = this.activeLanguageTab

        // 清空其他语言的数据
        this.supportedLanguages.forEach(lang => {
          if (lang.value !== currentLanguageCode) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].id : null
            const originalRelationGroupId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].relationGroupId : this.currentRelationGroupId

            // 创建新的表单数据
            this.$set(this.multiLanguageForms, lang.value, {
              id: originalId,
              positionTitle: null,
              cmsMenuId: this.multiLanguageForms[currentLanguageCode].cmsMenuId, // 保持cms菜单ID一致
              description: null,
              peopleNumber: null,
              workAddress: null,
              publicDate: null,
              content: null,
              status: '0',
              orderNum: null,
              languageType: lang.value,
              createBy: null,
              createTime: null,
              updateTime: null,
              updateBy: null,
              delFlag: null,
              externalLinks: null,
              remark: null,
              relationGroupId: originalRelationGroupId
            })
          }
        })

        this.$modal.msgSuccess('清空成功')
      }).catch(() => { })
    }
  }
}
</script>

<template>
  <div class="com-image-drag">
    <div class="button-list" v-if="needDrag">
      <el-button
        v-if="!drag_open"
        :disabled="imgList.length <= 1"
        type="text"
        size="small"
        class="operation-success"
        @click="openDrag"
      >拖拽
      </el-button>
      <el-button
        v-if="drag_open"
        type="text"
        size="small"
        class="operation-success"
        @click="save"
      >保存
      </el-button>
      <el-button
        v-if="drag_open"
        type="text"
        size="small"
        class="operation-error"
        @click="cancle"
      >取消
      </el-button>
    </div>
    <div class="image-list">
      <!-- 拖拽层 -->
      <div v-show="drag_open" class="list-wrap">
        <draggable
          v-model="imgList"
          :options="{
            animation: 150,
            ghostClass: 'sortable-ghost',
            chosenClass: 'chosenClass',
            scroll: true,
            scrollSensitivity: 200
          }"
          @ended="datadragEnd"
        >
          <div
            v-for="($item, $index) in imgList"
            :key="$index"
            class="image-item"
            :style="{ backgroundImage: `url(${$item.url})` }"
          />
        </draggable>
      </div>
      <!-- 展示层 -->
      <div v-show="!drag_open" class="list-wrap">
        <div
          v-for="($item, $index) in imgList"
          :key="$index"
          class="image-item"
          :style="{ backgroundImage: `url(${$item.url})` }"
          @mouseover.prevent="$item.is_hover = true"
          @mouseleave.prevent="$item.is_hover = false"
        >
          <div v-show="!$item.is_hover" class="label">
            <i class="el-icon-upload-success el-icon-check icon-success"/>
          </div>
          <div v-show="$item.is_hover" class="mask">
            <i class="el-icon-delete bin" @click="deleteImage($index)"/>
          </div>
        </div>
        <el-upload
          ref="upload"
          list-type="picture-card"
          name="file"
          class="upload-machine"
          :limit="limit"
          :data="tokens"
          accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF"
          :action="baseUpload"
          :on-success="successUpload"
          :before-upload="beforeUpload"
          :on-remove="fileRemove"
          :show-file-list="false"
          :multiple="true"
          enctype="multipart/form-data"
          :on-exceed="exceedHandle"
          :file-list="imgList"
          v-show="imgList.length<limit"
        >
          <i
            class="el-icon-plus avatar-uploader-icon"
          />
        </el-upload>
      </div>
    </div>
  </div>
</template>
<script>
/**
 * <AUTHOR>
 * @description 为了方便上传图片组件可拖拽排序，不改变饿了么插件的逻辑，只做视图层的展示
 * @param {Array} list 图片数组
 * @param {Number} limit 最多可上传几张图片
 * @param {Function} action 上传接口地址
 * @param {Boolean} multiple 是否批量上传
 * @param {Function} beforeUpload 上传之前的回调，用于校验
 * @param {Function} onSuccess 上传成功的回调函数
 * @param {Function} onError 上传失败的回调函数
 */
import draggable from 'vuedraggable';
import { getUploadToken } from '@/api/upload/uploadApi';

export default {
  name: 'ComImageShow',
  components: {
    draggable
  },
  props: {
    imgUrl: {
      type: String,
      default() {
        return null;
      }
    },
    //是否需要开启拖拽
    needDrag: {
      type: Boolean,
      default() {
        return false;
      }
    },
    cloudType: {
      type: String,
      default() {
        return process.env.VUE_APP_CLOUD_TYPE;
      }
    },
    limit: {
      type: Number,
      default() {
        return 1;
      }
    },
    /*width: {
      type: Number,
      default() {
        return 120;
      }
    },
    height: {
      type: Number,
      default() {
        return 120;
      }
    }*/
  },
  data() {
    return {
      progressPercent: 0,
      imgList: [], // 拖拽插件不建议直接改变父组件的传值，所以另建一个新数组
      file_list: [], // 保存开启拖拽之前排序的数组
      drag_open: false, // 拖拽开启开关
      imgArr: [],
      tokens: {},
      baseFileUrl: '',
      baseUpload: '',
      img: '',
      tempList: [],

    };
  },
  watch: {
    imgUrl() {
      this.img = this.imgUrl;
      // console.log(this.imgUrl, '=========watch44444====');
      if (this.imgUrl) {
        this.imgArr = this.imgUrl.split(";");
        this.imgList = this.imgUrl.split(";").map(url => {
          const obj = {
            url: url,
            is_hover: false
          };
          return obj;
        });
      }else {
        this.imgArr = []
        this.imgList=[]
      }
    }
  },
  mounted() {
    this.img = this.imgUrl;
    // console.log(this.imgUrl, '=========mounted====');
    if (this.imgUrl) {
      this.imgArr = this.imgUrl.split(";");
      // 初始数组拷贝
      this.imgList = this.imgUrl.split(";").map(url => {
        const obj = {
          url: url,
          is_hover: false
        };
        return obj;
      });
    }else {
      this.imgArr = []
      this.imgList=[]
    }
    // console.log(this.imgList, '============');
  },

  methods: {
    /**
     * 文件超出个数限制时的钩子
     *
     * @param file
     */
    exceedHandle(files, fileList) {
      console.log(files)
      console.log('limit')
      console.log(fileList)
      this.$message({
        message: `最多上传${this.limit}项`,
        type: 'warning'
      });
    },
    fileRemove(file,fileList){
      console.log('remove',fileList)
    },
    beforeUpload(file) {

      if (this.cloudType == 'oss') {
        return new Promise((resolve, reject) => {
          const fileForm = {value: file.lastModified + file.name};
          getUploadToken(fileForm).then(res => {
            console.log('file', file);

            this.tokens.key = res.data.key;
            this.tokens.OSSAccessKeyId = res.data.params.OSSAccessKeyId;
            this.tokens.policy = res.data.params.policy;
            this.tokens.signature = res.data.params.signature;
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tokens.success_action_status = res.data.params.success_action_status;
            this.tempList.push({
              key: res.data.key,
              token: res.data.params.token
            });
            resolve(true);
          }, _ => {
            reject(false);
          });
        });
      }
      if (this.cloudType == 'cos') {
        console.log('cos')
        const fileForm = {value: file.lastModified + file.name};
        const that = this;
        getUploadToken(fileForm).then(res => {
          this.data = res.data;
          var cos = new COS({
            SecretId: that.data.params.tokenJson.credentials.tmpSecretId,
            SecretKey: that.data.params.tokenJson.credentials.tmpSecretKey,
            XCosSecurityToken: that.data.params.tokenJson.credentials.sessionToken
          });
          const key = that.data.key;
          cos.putObject(
            {
              Bucket: that.data.params.bucket,
              Region: that.data.params.region,
              Key: key,
              Body: file
            },
            function (err, data) {
              if (data) {
                console.log('1111', data)
                that.imgList.push(`http://${data.Location}`)
                that.$emit('update:imgArr', that.imgList.join(";"));
              }
              console.log(132, err || data);
            }
          );
        });
      }
      if (this.cloudType == 'obs') {
        return new Promise((resolve, reject) => {
          const fileForm = {
            value: file.lastModified + file.name
          };
          getUploadToken(fileForm).then(res => {
            this.tokens.key = res.data.key;
            this.tokens.token = res.data.params.token
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tempList.push({
              key: res.data.key,
              token: res.data.params.token
            });
            resolve(true);
          }, _ => {
            reject(false);
          });
        });
      }
    },

    successUpload(res, file, fileList) {
      let obj = {};
      let isFinish = true
      fileList.map(item=>{
        if(item.status !== 'success'){
          isFinish = false
        }
      })
      if(isFinish){
        if (this.cloudType !== 'cos') {
          fileList.map(item=>{
            this.progressPercent = 0;
            this.img = `${this.baseFileUrl}/${this.tokens.key}`;
            obj = {
              url: `${this.baseFileUrl}/${this.tempList[0].key}`,
              is_hover: false
            };
            this.imgList.push(obj);
            this.$emit('update:imgUrl', this.imgList.map(item => item.url).join(";"));
            this.tempList.splice(0, 1)
          })
          console.log(111, this.$refs.upload.uploadFiles)
        }
      }
    },

    // 删除图片
    deleteImage(i) {
      console.log('deleteImg')
      this.imgList.splice(i, 1);
      this.$emit('update', this.imgList.map(item => item.url));
      this.$emit('update:imgUrl', this.imgList.map(item => item.url).join(";"));
      // this.$refs.upload.clearFiles()
      this.$refs.upload.uploadFiles.splice(i,1)//上传列表对应删除，否则limit错乱
    },
    // 开启拖拽
    openDrag() {
      Object.assign(this.file_list, this.imgList);
      this.drag_open = true;
    },
    // 取消拖拽
    cancle() {
      Object.assign(this.imgList, this.file_list);
      this.drag_open = false;
    },
    datadragEnd(evt) {
      console.log(this.imgList, '=======1=====');
      const temp = this.imgList[evt.newIndex];
      this.imgList[evt.newIndex] = this.imgList[evt.oldIndex];
      this.imgList[evt.oldIndex] = temp;
      console.log(this.imgList, '======2======');
    },
    datarageStart(evt) {
      console.log(this.imgList, '=======0=====');
      // const temp = this.imgList[evt.newIndex];
      // this.imgList[evt.newIndex] = this.imgList[evt.oldIndex];
      // this.imgList[evt.oldIndex] = temp;
      console.log(evt, 22222222);
    },
    // 拖拽保存
    save() {
      this.$emit('update', this.imgList.map(item => item.url).join(";"));
      this.$emit('update:imgUrl', this.imgList.map(item => item.url).join(";"));
      this.drag_open = false;
    }
  }
};
</script>
<style lang="sass" scoped>
.com-image-drag
  &:after
    display: block
    clear: both
    content: ""

  .image-list
    float: left

    &:after
      display: block
      clear: both
      content: ""

    .list-wrap
      float: left

    .image-item
      width: 148px
      height: 148px
      position: relative
      margin-right: 10px
      margin-bottom: 10px
      border: 1px solid #c0ccda
      background-size: 100% 100%
      border-radius: 6px
      float: left
      overflow: hidden
      cursor: pointer

      .label
        width: 46px
        height: 26px
        background-color: #13ce66
        color: #FFFFFF
        transform: rotate(45deg)
        text-align: center
        position: absolute
        right: -17px
        top: -7px

        .icon-success
          transform: rotate(-45deg)

      .mask
        width: 100%
        height: 100%
        border-radius: 6px
        background-color: rgba(0, 0, 0, 0.5)
        position: relative

        .bin
          color: #FFFFFF
          font-size: 20px
          position: absolute
          left: 45%
          top: 43%

  .upload-machine
    float: left
</style>

<template>
  <div :class="{fullscreen:fullscreen}" class="tinymce-container" :style="{width:containerWidth}">
    <textarea :id="tinymceId" class="tinymce-textarea" />
    <div class="editor-custom-btn-container" style="z-index: 1000000;">
      <editorImage color="#1890ff" class="editor-upload-btn" @successCBK="imageSuccessCBK" />
    </div>
  </div>
</template>

<script>
/**
 * docs:
 * https://panjiachen.github.io/vue-element-admin-site/feature/component/rich-editor.html#tinymce
 */
import editorImage from './components/EditorImage';
import plugins from './plugins';
import toolbar from './toolbar';
import load from './dynamicLoadScript';
import axios from 'axios';
import { getUploadToken } from "../../api/upload/uploadApi";
// why use this cdn, detail see https://github.com/PanJiaChen/tinymce-all-in-one
// const tinymceCDN = 'https://cdn.jsdelivr.net/npm/tinymce-all-in-one@4.9.3/tinymce.min.js'
const tinymceCDN = '/tinymce/tinymce.min.js';

export default {
  name: 'Tinymce',
  components: { editorImage },
  props: {
    id: {
      type: String,
      default: function() {
        return 'vue-tinymce-' + +new Date() + ((Math.random() * 1000).toFixed(0) + '');
      }
    },
    readonly: {
      type: Boolean,
      default: false
    },
    value: {
      type: String,
      default: ''
    },
    toolbar: {
      type: Array,
      required: false,
      default() {
        return [];
      }
    },
    menubar: {
      type: String,
      default: 'file edit format'
    },
    height: {
      type: [Number, String],
      required: false,
      default: 360
    },
    width: {
      type: [Number, String],
      required: false,
      default: 'auto'
    }
  },
  data() {
    return {
      baseUpload: '',
      baseFileUrl: '',
      tokens: {},
      hasChange: false,
      hasInit: false,
      tinymceId: this.id,
      fullscreen: false,
      languageTypeList: {
        'en': 'en',
        'zh': 'zh_CN',
        'es': 'es_MX',
        'ja': 'ja'
      }
    };
  },
  computed: {
    containerWidth() {
      const width = this.width;
      if (/^[\d]+(\.[\d]+)?$/.test(width)) { // matches `100`, `'100'`
        return `${width}px`;
      }
      return width;
    }
  },
  watch: {
    value(val) {
      if (!this.hasChange && this.hasInit) {
        this.$nextTick(() =>
          window.tinymce.get(this.tinymceId).setContent(val || ''));
      }
    },
    readonly(val) {
      if (!this.hasChange && this.hasInit) {
        console.log('this.readonly');
      }
    }
  },
  mounted() {
    this.init();
  },
  activated() {
    if (window.tinymce) {
      this.initTinymce();
    }
  },
  deactivated() {
    this.destroyTinymce();
  },
  destroyed() {
    this.destroyTinymce();
  },
  methods: {
    init() {
      // dynamic load tinymce from cdn
      load(tinymceCDN, (err) => {
        if (err) {
          this.$message.error(err.message);
          return;
        }
        this.initTinymce();
      });
    },
    initTinymce: function() {
      const _this = this;
      window.tinymce.init({
        selector: `#${this.tinymceId}`,
        language: this.languageTypeList['zh'],
        height: this.height,
        body_class: 'panel-body ',
        object_resizing: false,
        toolbar: this.toolbar.length > 0 ? this.toolbar : toolbar,
        menubar: this.menubar,
        readonly: this.readonly,
        plugins: plugins,
        // paste_data_images: false, // 设置为“true”将允许粘贴图像，而将其设置为“false”将不允许粘贴图像。
        end_container_on_empty_block: true,
        powerpaste_allow_local_images: true,
        powerpaste_word_import: 'clean',
        code_dialog_height: 450,
        code_dialog_width: 1000,
        advlist_bullet_styles: 'square',
        advlist_number_styles: 'default',
        // imagetools_cors_hosts: ['www.tinymce.com', 'codepen.io'],
        default_link_target: '_blank',
        link_title: false,
        convert_fonts_to_spans: false,
        // extended_valid_elements: 'span',
        force_br_newlines: false,
        force_p_newlines: false,
        nonbreaking_force_tab: true, // inserting nonbreaking space &nbsp; need Nonbreaking Space Plugin
        init_instance_callback: editor => {
          if (_this.value) {
            editor.setContent(_this.value);
          }
          _this.hasInit = true;
          editor.on('NodeChange Change KeyUp SetContent', () => {
            this.hasChange = true;
            this.$emit('input', editor.getContent());
          });
        }, // 上传图片
        images_upload_handler: (blobInfo, success, failure) => {
          this.uploadFile(blobInfo).then(res => {
            console.log(res);
            if (res.status) {
              success(res.formData.data.params.host + '/' + res.formData.data.key);
              return;
            }
            failure('上传失败');
          }, err => {
            console.log(err);
            failure('上传出错');
          });
        },
        setup(editor) {
          editor.on('FullscreenStateChanged', (e) => {
            _this.fullscreen = e.state;
          });
        }
      });
    },
    beforUpload(blobInfo) {
      const that = this;
      return new Promise((resolve, reject) => {
        getUploadToken({ value: new Date().getTime() + '.png' })
          .then((res) => {
            console.log(res, '-------upload/token------');
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tokens.key = res.data.key;
            this.tokens.token = res.data.params.token;
            const options = { status: true, formData: res };
            resolve(options);
          }, _ => {
            reject({ status: false });
          });
      });
    },
    uploadFile: async function(blobInfo) {
      const that = this;
      const tokenInfo = await that.beforUpload(blobInfo);
      console.log('-----45********');
      console.log(tokenInfo);
      return new Promise((resolve, reject) => {
        if (tokenInfo.status) {
          const formData = new FormData();
          formData.append('key', tokenInfo.formData.data.key);
          formData.append('token', tokenInfo.formData.data.params.token);
          // formData.append('OSSAccessKeyId', tokenInfo.formData.data.params.OSSAccessKeyId);
          // formData.append('policy', tokenInfo.formData.data.params.policy);
          // formData.append('signature', tokenInfo.formData.data.params.signature);
          // formData.append('success_action_status', tokenInfo.formData.data.params.success_action_status);
          formData.append('file', blobInfo.blob());
          axios({
            url: tokenInfo.formData.data.params.host,
            header: {
              'Content-Type': 'multipart/form-data'
            },
            method: 'post',
            data: formData
          })
            .then(r => {
              console.log('ok====');
              console.log(r);
              if (r.status >= 200 && r.status < 300) {
                resolve({ status: true, formData: tokenInfo.formData });
              }
              reject({ status: false });
            }, e => {
              console.log('error====');
              console.log(e);
              reject({ status: false });
            });
        } else {
          reject({ status: false });
        }
      });
    },
    destroyTinymce() {
      const tinymce = window.tinymce.get(this.tinymceId);
      if (this.fullscreen) {
        tinymce.execCommand('mceFullScreen');
      }

      if (tinymce) {
        tinymce.destroy();
      }
    },
    setContent(value) {
      window.tinymce.get(this.tinymceId).setContent(value);
    },
    getContent() {
      window.tinymce.get(this.tinymceId).getContent();
    },
    imageSuccessCBK(arr) {
      const _this = this;
      console.log("------imageSuccessCBK-------")
      console.log(arr)
      arr.forEach(v => {
        window.tinymce.get(_this.tinymceId).insertContent(`<img class="wscnph" src="${v.url}" >`);
      });
    }
  }
};
</script>

<style scoped>
.tinymce-container {
  position: relative;
  line-height: normal;
}

.tinymce-container >>> .mce-fullscreen {
  z-index: 10000;
}

.tinymce-textarea {
  visibility: hidden;
  z-index: -1;
}

.editor-custom-btn-container {
  position: absolute;
  right: 4px;
  top: 4px;
  /*z-index: 2005;*/
}

.fullscreen .editor-custom-btn-container {
  z-index: 1000000000;
  position: fixed;
}

.editor-upload-btn {
  display: inline-block;
}
</style>

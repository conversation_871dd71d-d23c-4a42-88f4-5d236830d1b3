import request from '@/utils/request'

// 查询菜单管理列表
export function listCmsMenu(query) {
  return request({
    url: '/yq/cmsMenu/list',
    method: 'get',
    params: query
  })
}

// cms菜单下拉框
export function cmsMenuList(query) {
  return request({
    url: '/yq/cmsMenu/cmsMenuList',
    method: 'get',
    params: query
  })
}

// 菜单列表树形
export function cmsMenuTreeselect(query) {
  return request({
    url: '/yq/cmsMenu/treeselect',
    method: 'get',
    params: query
  })
}

// 查询菜单管理详细
export function getCmsMenu(id) {
  return request({
    url: '/yq/cmsMenu/detail/' + id,
    method: 'get'
  })
}

// 新增菜单管理
export function addCmsMenu(data) {
  return request({
    url: '/yq/cmsMenu/add',
    method: 'post',
    data: data
  })
}

// 修改菜单管理
export function updateCmsMenu(data) {
  return request({
    url: '/yq/cmsMenu/edit',
    method: 'put',
    data: data
  })
}

// 删除菜单管理
export function delCmsMenu(id) {
  return request({
    url: '/yq/cmsMenu/del/' + id,
    method: 'delete'
  })
}
// 自动翻译文章数据（用于前端传递的原始数据）
export function autoTranslateCmsMenuData(data) {
  return request({
    url: '/yq/cmsMenu/translateMenuData',
    method: 'post',
    data: data
  })
}

// 批量保存多语言版本的文章
export function batchSaveMultiLanguage(data) {
  return request({
    url: '/yq/cmsMenu/batchSaveMultiLanguage',
    method: 'post',
    data: data
  })
}

// 根据关系组ID获取所有语言版本
export function getByRelationGroup(relationGroupId) {
  return request({
    url: `/yq/cmsMenu/getByRelationGroupId/${relationGroupId}`,
    method: 'get'
  })
}


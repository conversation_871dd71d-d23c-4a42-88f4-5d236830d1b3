import request from '@/utils/request'

// 查询新闻列表
export function listArticle(query) {
  return request({
    url: '/yq/article/list',
    method: 'get',
    params: query
  })
}

// 查询新闻详细
export function getArticle(id) {
  return request({
    url: '/yq/article/' + id,
    method: 'get'
  })
}

// 通过文章类型id，获取文章下拉框数据
export function selectYqArticleByArticleType(articleTypeId) {
  return request({
    url: '/yq/article/articleByArticleType/' + articleTypeId,
    method: 'get'
  })
}

// 新增新闻
export function addArticle(data) {
  return request({
    url: '/yq/article',
    method: 'post',
    data: data
  })
}

// 修改新闻
export function updateArticle(data) {
  return request({
    url: '/yq/article',
    method: 'put',
    data: data
  })
}

// 删除新闻
export function delArticle(id) {
  return request({
    url: '/yq/article/' + id,
    method: 'delete'
  })
}

// 自动翻译文章数据（用于前端传递的原始数据）
export function autoTranslateArticleData(data) {
  return request({
    url: '/yq/article/autoTranslateData',
    method: 'post',
    data: data
  })
}

// 批量保存多语言版本的文章
export function batchSaveMultiLanguage(data) {
  return request({
    url: '/yq/article/batchSave',
    method: 'post',
    data: data
  })
}

// 根据关系组ID获取所有语言版本
export function getByRelationGroup(relationGroupId) {
  return request({
    url: `/yq/article/getByRelationGroup/${relationGroupId}`,
    method: 'get'
  })
}

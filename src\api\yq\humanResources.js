import request from '@/utils/request'

// 查询人力资源列表
export function listHumanResources(query) {
  return request({
    url: '/yq/humanResources/list',
    method: 'get',
    params: query
  })
}

// 获取人力资源菜单
export function getHumanResourcesMenus() {
  return request({
    url: '/yq/humanResources/humanResourcesMenus',
    method: 'get',
  })
}

// 查询人力资源详细
export function getHumanResources(id) {
  return request({
    url: '/yq/humanResources/detail/' + id,
    method: 'get'
  })
}

// 新增人力资源
export function addHumanResources(data) {
  return request({
    url: '/yq/humanResources/add',
    method: 'post',
    data: data
  })
}

// 修改人力资源
export function updateHumanResources(data) {
  return request({
    url: '/yq/humanResources/edit',
    method: 'put',
    data: data
  })
}

// 删除人力资源
export function delHumanResources(id) {
  return request({
    url: '/yq/humanResources/del/' + id,
    method: 'delete'
  })
}

// 自动翻译文章数据（用于前端传递的原始数据）
export function autoTranslateHumanResourcesData(data) {
  return request({
    url: '/yq/humanResources/translateHumanResourcesData',
    method: 'post',
    data: data
  })
}

// 批量保存多语言版本的文章
export function batchSaveMultiLanguage(data) {
  return request({
    url: '/yq/humanResources/batchSave',
    method: 'post',
    data: data
  })
}

// 根据关系组ID获取所有语言版本
export function getByRelationGroup(relationGroupId) {
  return request({
    url: `/yq/humanResources/getByRelationGroupId/${relationGroupId}`,
    method: 'get'
  })
}
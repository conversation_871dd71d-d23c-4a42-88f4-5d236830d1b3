<template>
<el-dialog
  :visible.sync="show"
  custom-class="sms-verify-modal"
  width="400px"
  title="需要验证您的身份"
  :close-on-click-modal="false"
  :show-close="false">
  <el-row class="mb20">
    为了您的帐号安全，请进行身份验证。
  </el-row>
  <el-row class="mb20">
    {{phoneNumber}}
  </el-row>
  <el-form :model="form" :rules="rules" ref="verifyForm">
    <el-form-item prop="code">
      <el-input v-model="form.code">
        <template slot="suffix">
          <el-button type="text" @click="sendSMS" :disabled="!verifyClickable">
            {{ verifyClickable? '获取验证码': `${count}秒后重发` }}
          </el-button>
        </template>
      </el-input>
    </el-form-item>
  </el-form>
  <div slot="footer">
    <el-button type="info" plain @click="show = false">
      取消
    </el-button>
    <el-button type="primary" plain @click="confirmForm">
      确认
    </el-button>
  </div>
</el-dialog>
</template>

<script>
import { sendSMSCode } from "@/api/login";

export default {
  name: "smsVerifyModal",
  data() {
    return {
      phoneNumber: '',
      token: '',

      form: {},

      show: false,

      rules: {
        code: [
          { required: true, trigger: "change", message: "请输入验证码" },
          { len: 4, trigger: "blur", message: "验证码为4位" }
        ]
      },

      timer: null,
      count: '',
      verifyClickable: true,
      loading: true
    }
  },
  methods: {
    open(phoneNumber, token) {
      this.resetForm()
      this.phoneNumber = phoneNumber
      this.form.token = token
      this.show = true
    },
    resetForm() {
      this.form = {
        code: '',
        token: '',
      }
    },
    timerCountDown() {
      const times = 60; // 倒计时时间
      if (!this.timer) {
        this.count = times;
        this.verifyClickable = false;
        this.timer = setInterval(() => {
          if (this.count > 0 && this.count <= times) {
            this.count--;
          } else {
            this.verifyClickable = true;
            clearInterval(this.timer);
            this.timer = null;
          }
        }, 1000)
      }
    },
    sendSMS() {
      sendSMSCode({token: this.form.token}).then(res => {
        this.$modal.msgSuccess("发送成功")
        this.timerCountDown()
      }).catch(e => {
        if (e.code === 401) {
          this.show = false
        }
      })
    },
    confirmForm() {
      this.$refs.verifyForm.validate(valid => {
        if (valid) {
          this.loading = true
          this.$store.dispatch("verifySMS", this.form).then((resData) => {
            this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
          }).catch(() => {
            this.loading = false;
          })
        }
      })
    }
  }

}
</script>

<style lang="scss">
.sms-verify-modal {

}
</style>

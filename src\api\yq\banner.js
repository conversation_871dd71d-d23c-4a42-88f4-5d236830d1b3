import request from '@/utils/request'

// 查询轮播（广告）图列表
export function listBanner(query) {
  return request({
    url: '/yq/banner/list',
    method: 'get',
    params: query
  })
}

// 查询轮播（广告）图详细
export function getBanner(id) {
  return request({
    url: '/yq/banner/' + id,
    method: 'get'
  })
}

// 新增轮播（广告）图
export function addBanner(data) {
  return request({
    url: '/yq/banner',
    method: 'post',
    data: data
  })
}

// 修改轮播（广告）图
export function updateBanner(data) {
  return request({
    url: '/yq/banner',
    method: 'put',
    data: data
  })
}

// 删除轮播（广告）图
export function delBanner(id) {
  return request({
    url: '/yq/banner/' + id,
    method: 'delete'
  })
}

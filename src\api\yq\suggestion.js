import request from '@/utils/request'

// 查询意见建议反馈列表
export function listSuggestion(query) {
  return request({
    url: '/yq/suggestion/list',
    method: 'get',
    params: query
  })
}

// 查询意见建议反馈详细
export function getSuggestion(id) {
  return request({
    url: '/yq/suggestion/' + id,
    method: 'get'
  })
}

// 新增意见建议反馈
export function addSuggestion(data) {
  return request({
    url: '/yq/suggestion',
    method: 'post',
    data: data
  })
}

// 修改意见建议反馈
export function updateSuggestion(data) {
  return request({
    url: '/yq/suggestion',
    method: 'put',
    data: data
  })
}

// 删除意见建议反馈
export function delSuggestion(id) {
  return request({
    url: '/yq/suggestion/' + id,
    method: 'delete'
  })
}

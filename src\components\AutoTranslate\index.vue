<template>
  <div class="auto-translate-container">
    <!-- 自动翻译按钮 -->
    <el-button
      type="primary"
      icon="el-icon-s-promotion"
      :loading="translating"
      @click="handleAutoTranslate"
      :disabled="!canTranslate"
    >
      {{ translating ? '翻译中...' : '自动翻译' }}
    </el-button>

    <!-- 翻译结果展示 -->
    <div v-if="translationResults.length > 0" class="translation-results">
      <el-divider content-position="left">翻译结果</el-divider>

      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane
          v-for="result in translationResults"
          :key="result.languageType"
          :label="getLanguageLabel(result.languageType)"
          :name="result.languageType"
        >
          <div class="translation-content">
            <!-- 标题翻译 -->
            <el-form-item v-if="result.title" label="标题">
              <el-input
                v-model="result.title"
                placeholder="请输入标题"
                @input="handleTranslationEdit(result.languageType, 'title', $event)"
              />
            </el-form-item>

            <!-- 名称翻译（用于文章类型） -->
            <el-form-item v-if="result.name" label="名称">
              <el-input
                v-model="result.name"
                placeholder="请输入名称"
                @input="handleTranslationEdit(result.languageType, 'name', $event)"
              />
            </el-form-item>

            <!-- 描述翻译 -->
            <el-form-item v-if="result.description" label="描述">
              <el-input
                v-model="result.description"
                type="textarea"
                :rows="3"
                placeholder="请输入描述"
                @input="handleTranslationEdit(result.languageType, 'description', $event)"
              />
            </el-form-item>

            <!-- 内容翻译 -->
            <el-form-item v-if="result.content" label="内容">
              <el-input
                v-model="result.content"
                type="textarea"
                :rows="6"
                placeholder="请输入内容"
                @input="handleTranslationEdit(result.languageType, 'content', $event)"
              />
            </el-form-item>

            <!-- 产品特色翻译 -->
            <el-form-item v-if="result.productFeatures" label="产品特色">
              <el-input
                v-model="result.productFeatures"
                type="textarea"
                :rows="3"
                placeholder="请输入产品特色"
                @input="handleTranslationEdit(result.languageType, 'productFeatures', $event)"
              />
            </el-form-item>

            <!-- 营养配方翻译 -->
            <el-form-item v-if="result.nutritionRecipe" label="营养配方">
              <el-input
                v-model="result.nutritionRecipe"
                type="textarea"
                :rows="3"
                placeholder="请输入营养配方"
                @input="handleTranslationEdit(result.languageType, 'nutritionRecipe', $event)"
              />
            </el-form-item>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button @click="handleClearResults">清空翻译</el-button>
        <el-button type="primary" @click="handleConfirmTranslation">确认翻译</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { autoTranslateArticle, autoTranslateArticleData } from '@/api/yq/article'
import { autoTranslateArticleType, autoTranslateArticleTypeData } from '@/api/yq/articleType'

export default {
  name: 'AutoTranslate',
  props: {
    // 实体ID（用于已保存的数据）
    entityId: {
      type: [Number, String],
      default: null
    },
    // 实体类型：article 或 article_type
    entityType: {
      type: String,
      required: true,
      validator: value => ['article', 'article_type'].includes(value)
    },
    // 原始数据（用于新增时的翻译）
    originalData: {
      type: Object,
      default: () => ({})
    },
    // 是否可以翻译
    canTranslate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      translating: false,
      translationResults: [],
      activeTab: '',
      languageLabels: {
        'zh_CN': '简体中文',
        'zh_HK': '繁体中文',
        'en_US': '英语(美国)'
      }
    }
  },
  methods: {
    // 处理自动翻译
    async handleAutoTranslate() {
      if (!this.canTranslate) {
        this.$message.warning('请先填写必要信息')
        return
      }

      this.translating = true

      try {
        let response

        if (this.entityId) {
          // 已保存的数据，调用后端翻译接口
          if (this.entityType === 'article') {
            response = await autoTranslateArticle(this.entityId)
          } else {
            response = await autoTranslateArticleType(this.entityId)
          }
        } else {
          // 新增数据，传递原始数据进行翻译
          if (this.entityType === 'article') {
            response = await autoTranslateArticleData(this.originalData)
          } else {
            response = await autoTranslateArticleTypeData(this.originalData)
          }
        }

        if (response.code === 200 && response.data) {
          this.translationResults = response.data
          if (this.translationResults.length > 0) {
            this.activeTab = this.translationResults[0].languageType
          }
          this.$message.success('翻译完成')
        } else {
          this.$message.error(response.msg || '翻译失败')
        }
      } catch (error) {
        console.error('翻译错误:', error)
        this.$message.error('翻译服务异常，请稍后重试')
      } finally {
        this.translating = false
      }
    },

    // 处理翻译内容编辑
    handleTranslationEdit(languageType, field, value) {
      const result = this.translationResults.find(r => r.languageType === languageType)
      if (result) {
        result[field] = value
      }
    },

    // 获取语言标签
    getLanguageLabel(languageType) {
      return this.languageLabels[languageType] || languageType
    },

    // 清空翻译结果
    handleClearResults() {
      this.translationResults = []
      this.activeTab = ''
    },

    // 确认翻译
    handleConfirmTranslation() {
      this.$emit('translation-confirmed', this.translationResults)
    }
  }
}
</script>

<style scoped>
.auto-translate-container {
  margin: 20px 0;
}

.translation-results {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fafafa;
}

.translation-content {
  background-color: white;
  padding: 20px;
  border-radius: 4px;
}

.action-buttons {
  margin-top: 20px;
  text-align: right;
}

.action-buttons .el-button {
  margin-left: 10px;
}
</style>

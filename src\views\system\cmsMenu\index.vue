<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="中文菜单" name="first"></el-tab-pane>
      <el-tab-pane label="其他语言菜单" name="second"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="菜单状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择菜单状态" clearable size="small">
          <el-option v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="系统内置" prop="beSystem">
        <el-select v-model="queryParams.beSystem" placeholder="请选择系统内置" clearable size="small">
          <el-option v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
        <el-select filterable v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
          <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd(null)"
          v-hasPermi="['yq:cmsMenu:add']">新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table max-height="700" v-loading="loading" default-expand-all row-key="id"
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :data="cmsMenuList"
      @selection-change="handleSelectionChange">
      <el-table-column width="120" fixed="left" label="菜单编号" align="center" prop="id" />
      <el-table-column label="菜单图标" align="center" prop="iconUrl" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.iconUrl" :src="scope.row.iconUrl" :width="50" :height="50" />
          <div v-else style="display: flex;flex-direction: column;align-items: center;" slot="error">
            <img style="width: 50px;height: 50px;" src="@/assets/404_images/none.png" alt="">
            <span>暂无数据</span>
          </div>
        </template>
      </el-table-column>
      <!--      <el-table-column label="位置" align="center" prop="position">-->
      <!--        <template slot-scope="scope">-->
      <!--          <dict-tag :options="dict.type.menu_position_dict" :value="scope.row.position"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column fixed="left" width="200" label="名称" align="center" prop="name" />
      <el-table-column width="200" label="菜单编码" align="center" prop="code" />
      <el-table-column width="200" label="跳转链接" align="center" prop="routeUrl" />
      <el-table-column show-overflow-tooltip width="200" label="简介" align="center" prop="introduction" />
      <el-table-column width="90" label="菜单级别" align="center" prop="menuLevel" />
      <el-table-column width="200" label="语言类型" align="center" prop="languageType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType" />
        </template>
      </el-table-column>
      <el-table-column width="200" label="系统内置" align="center" prop="beSystem">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.beSystem" />
        </template>
      </el-table-column>
      <!--      <el-table-column width="100" label="是否固定菜单" align="center" prop="beDefault">-->
      <!--        <template slot-scope="scope">-->
      <!--          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.beDefault"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column width="100" label="菜单列表样式" align="center" prop="listType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.menu_list_type" :value="scope.row.listType" />
        </template>
      </el-table-column>
      <el-table-column width="120" label="所属内容分类" align="center" prop="articleType" />
      <el-table-column width="120" show-overflow-tooltip label="关联文章" align="center" prop="article" />
      <el-table-column width="100" label="点击到详情页" align="center" prop="hasDetail">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.hasDetail" />
        </template>
      </el-table-column>
      <el-table-column label="菜单状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
      </el-table-column>
      <el-table-column width="100" label="备注" align="center" prop="remark" />
      <el-table-column fixed="right" width="200" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.menuLevel < 4" size="mini" type="text" icon="el-icon-edit"
            @click="handleAdd(scope.row)" v-hasPermi="['yq:humanResources:edit']">新增子级
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:cmsMenu:edit']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCopy(scope.row)"
            v-hasPermi="['yq:cmsMenu:edit']">复制
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['yq:cmsMenu:remove']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改菜单管理对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <!-- 语言标签页和按钮 -->
      <div style="position: relative; margin-bottom: 20px;">
        <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
          @tab-click="handleLanguageTabClick">
          <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label"
            :name="lang.value"></el-tab-pane>
        </el-tabs>

        <div
          style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px; z-index: 1;">
          <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
            @click="handleAutoTranslate">自动翻译其他语言</el-button>
          <el-button size="mini" type="warning" icon="el-icon-delete"
            @click="handleClearOtherLanguages">清空其他语言</el-button>
        </div>
      </div>

      <el-form ref="form" :model="multiLanguageForms[activeLanguageTab] || form" :rules="rules" label-width="120px">
        <el-form-item label="父级菜单" prop="parentId">
          <treeselect v-model="(multiLanguageForms[activeLanguageTab] || form).parentId" :options="cmsMenuTree"
            :normalizer="normalizer" placeholder="请选择父级菜单">
            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
              :class="labelClassName">
              {{ node.label }} ： {{ node.raw.language }}
              <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
            </label>
          </treeselect>
        </el-form-item>
        <el-form-item label="菜单图标">
          <b-img-draggable :img-url.sync="(multiLanguageForms[activeLanguageTab] || form).iconUrl"
            :limit="1"></b-img-draggable>
        </el-form-item>
        <el-form-item label="是否系统内置">
          <el-radio-group v-model="(multiLanguageForms[activeLanguageTab] || form).beSystem">
            <el-radio v-for="dict in dict.type.sys_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 隐藏语言类型表单项 -->
        <el-form-item label="名称" prop="name">
          <el-input maxlength="50" v-model="(multiLanguageForms[activeLanguageTab] || form).name" placeholder="请输入名称"
            show-word-limit />
        </el-form-item>
        <el-form-item label="菜单编码" prop="code">
          <el-input maxlength="20" v-model="(multiLanguageForms[activeLanguageTab] || form).code" placeholder="请输入菜单编码"
            show-word-limit />
        </el-form-item>
        <el-form-item label="跳转链接" prop="routeUrl">
          <el-input maxlength="200" v-model="(multiLanguageForms[activeLanguageTab] || form).routeUrl" type="textarea"
            placeholder="请输入内容" show-word-limit />
        </el-form-item>
        <el-form-item label="简介" prop="introduction">
          <el-input :rows="6" maxlength="200" v-model="(multiLanguageForms[activeLanguageTab] || form).introduction"
            type="textarea" placeholder="请输入内容" show-word-limit />
        </el-form-item>
        <el-form-item label="菜单列表样式" prop="listType">
          <el-select v-model="(multiLanguageForms[activeLanguageTab] || form).listType" placeholder="请选择菜单列表样式"
            clearable size="small">
            <el-option v-for="dict in dict.type.menu_list_type" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属内容分类" prop="articleTypeId">
          <treeselect v-model="(multiLanguageForms[activeLanguageTab] || form).articleTypeId" :options="articleTypeTree"
            :normalizer="normalizer" :default-expand-level="1" placeholder="请选择所属内容分类" />
        </el-form-item>
        <el-form-item v-if="showArticle" label="关联文章" prop="articleId">
          <el-select filterable style="width: 100%" v-model="(multiLanguageForms[activeLanguageTab] || form).articleId"
            placeholder="请先选择内容分类" clearable size="small">
            <el-option v-for="item in articleArr" :key="item.value" :label="item.label" :value="item.value">
              <span style="float: left">{{ item.label }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.language }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="点击到详情页" prop="hasDetail">
          <el-select v-model="(multiLanguageForms[activeLanguageTab] || form).hasDetail" placeholder="请选择菜单状态" clearable
            size="small">
            <el-option v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.label"
              :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单状态">
          <el-radio-group v-model="(multiLanguageForms[activeLanguageTab] || form).status">
            <el-radio v-for="dict in dict.type.sys_normal_disable" :key="dict.value" :label="dict.value">{{ dict.label
              }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="(multiLanguageForms[activeLanguageTab] || form).orderNum" :min="0" :max="999999"
            :precision="0" placeholder="请输入排序" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input maxlength="20" v-model="(multiLanguageForms[activeLanguageTab] || form).remark" placeholder="请输入备注"
            show-word-limit />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCmsMenu, getCmsMenu, delCmsMenu, addCmsMenu, updateCmsMenu, cmsMenuTreeselect, autoTranslateCmsMenuData, batchSaveMultiLanguage, getByRelationGroup } from '@/api/yq/cmsMenu'
import { treeselect } from '@/api/yq/articleType'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { selectYqArticleByArticleType } from '@/api/yq/article'

export default {
  name: 'CmsMenu',
  dicts: ['sys_normal_disable', 'menu_position_dict', 'status_yes_no', 'sys_yes_no', 'menu_list_type', 'i18N_language'],
  components: {
    Treeselect
  },
  data() {
    return {
      activeName: 'first',
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      showLanguageType: false,
      showArticle: false,
      articleArr: [],
      // 总条数
      total: 0,
      // 菜单管理表格数据
      cmsMenuList: [],
      cmsMenuTree: [],
      articleTypeTree: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        listType: null,
        articleTypeId: null,
        status: null,
        beSystem: null,
        languageType: null
      },
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前激活的语言标签页
      activeLanguageTab: 'zh_CN',
      // 翻译状态
      translating: false,
      // 提交状态
      submitting: false,
      // 当前编辑的关系组ID
      currentRelationGroupId: null,
      // 是否已经翻译过(用于判断是否需要二次确认)
      hasTranslated: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        status: [
          { required: true, message: '是否已禁用 0否 1是不能为空', trigger: 'blur' }
        ],
        parentId: [
          { required: true, message: '父级菜单 不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '名称 不能为空', trigger: 'blur' }
        ],
        listType: [
          { required: true, message: '菜单列表样式 不能为空', trigger: 'change' }
        ],
        // routeUrl: [
        //   { required: true, message: '跳转链接 不能为空', trigger: 'blur' }
        // ],
        beDefault: [
          { required: true, message: '是否固定菜单 不能为空', trigger: 'change' }
        ],
        hasDetail: [
          { required: true, message: '点击跳转详情 不能为空', trigger: 'change' }
        ],
        articleId: [
          { required: true, message: '关联文章 不能为空', trigger: 'change' }
        ],
        languageType: [
          { required: true, message: '语言类型 不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  mounted() {
    // 等待字典数据加载完成后再初始化
    this.$nextTick(() => {
      this.waitForDictAndInitialize()
    })
  },
  computed: {
    // 从字典中获取支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  watch: {
    'form.hasDetail'(val) {
      if (val === '1') {
        this.showArticle = true
      } else {
        this.showArticle = false
        this.form.articleId = null
      }

    },

    'form.articleTypeId'(val) {
      //获取分类下的文章
      this.articleArr = []
      if (val) {
        selectYqArticleByArticleType(val).then(res => {
          if (res.data) {
            this.articleArr = res.data
          }
        })
      } else {
        this.form.articleId = null
      }

    }
  },
  methods: {
    // 等待字典数据加载并初始化
    waitForDictAndInitialize() {
      // 检查字典数据是否已加载
      if (this.dict && this.dict.type && this.dict.type.i18N_language && this.dict.type.sys_normal_disable) {
        this.initializeDefaultLanguage()
      } else {
        // 如果字典数据还未加载，延迟重试
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 50)
      }
    },
    // 初始化默认语言
    initializeDefaultLanguage() {
      const languages = this.dict.type.i18N_language
      // 优先选择中文，如果没有则选择第一个
      const zhCN = languages.find(lang => lang.value === 'zh_CN')
      this.activeLanguageTab = zhCN ? 'zh_CN' : languages[0].value
      this.initializeMultiLanguageForms()
    },
    // 初始化多语言表单
    initializeMultiLanguageForms() {
      const languages = this.dict.type.i18N_language
      languages.forEach(lang => {
        if (!this.multiLanguageForms[lang.value]) {
          this.$set(this.multiLanguageForms, lang.value, {
            id: null,
            parentId: null,
            iconUrl: null,
            position: null,
            name: null,
            routeUrl: null,
            introduction: null,
            beDefault: null,
            listType: null,
            articleTypeId: null,
            hasDetail: null,
            status: '0',
            menuLevel: 1,
            orderNum: null,
            createBy: null,
            articleId: null,
            createTime: null,
            updateTime: null,
            updateBy: null,
            delFlag: null,
            beSystem: 'N',
            languageType: lang.value,
            code: null,
            remark: null
          })
        }
      })
    },
    // 加载多语言数据
    loadMultiLanguageData(relationGroupId) {
      if (!relationGroupId) {
        return
      }

      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []
        // 清空现有数据
        this.multiLanguageForms = {}
        this.initializeMultiLanguageForms()

        // 填充已有的多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.$set(this.multiLanguageForms, item.languageType, { ...item })
          }
        })

        // 设置默认激活的tab为中文
        this.activeLanguageTab = 'zh_CN'
      }).catch(error => {
        this.$modal.msgError('加载多语言数据失败：' + (error.msg || error.message))
      })
    },

    handleClick(tab, event) {
      if (tab.index == 0) {
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },

    /** 转换新闻类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        name: node.label,
        children: node.children
      }
    },
    /**
     * 查询cms菜单树
     */
    getCmsMenuTreeselect() {
      cmsMenuTreeselect().then(response => {
        this.cmsMenuTree = []
        const data = { id: 0, label: '顶级节点', children: [] }
        data.children = response.data
        this.cmsMenuTree.push(data)
      })
    },

    /**
     * 查询文章类型菜单树
     */
    getArticleTypeTreeselect() {
      treeselect().then(response => {
        this.articleTypeTree = response.data
      })
    },

    /** 查询菜单管理列表 */
    getList() {
      this.loading = true
      listCmsMenu(this.queryParams).then(response => {
        this.cmsMenuList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        parentId: null,
        iconUrl: null,
        position: null,
        name: null,
        routeUrl: null,
        introduction: null,
        beDefault: null,
        listType: null,
        articleTypeId: null,
        hasDetail: null,
        status: '0',
        menuLevel: 1,
        orderNum: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        articleId: null,
        beSystem: 'N',
        languageType: null,
        code: null,
        remark: null
      }
      // 重置多语言表单数据
      this.multiLanguageForms = {}
      this.currentRelationGroupId = null
      // 重置翻译状态
      this.hasTranslated = false
      // 确保字典数据可用后再初始化
      if (this.dict && this.dict.type && this.dict.type.i18N_language) {
        this.initializeMultiLanguageForms()
      }

      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      if (row) {
        this.form.parentId = row.id
        // 设置所有语言表单的父级ID
        Object.keys(this.multiLanguageForms).forEach(lang => {
          this.multiLanguageForms[lang].parentId = row.id
        })
      }
      this.getCmsMenuTreeselect()
      this.getArticleTypeTreeselect()
      this.open = true
      this.title = '添加菜单管理'

      // 设置默认语言类型
      this.activeLanguageTab = 'zh_CN'
      if (this.multiLanguageForms[this.activeLanguageTab]) {
        this.multiLanguageForms[this.activeLanguageTab].languageType = this.activeLanguageTab
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCmsMenu(id).then(response => {
        const currentData = response.data

        // 检查是否有多语言关联关系
        if (currentData.relationGroupId) {
          this.currentRelationGroupId = currentData.relationGroupId
          // 获取所有语言版本
          this.loadMultiLanguageData(currentData.relationGroupId)
        } else {
          // 没有关联关系，只加载当前语言版本
          this.form = currentData
          if (this.form) {
            this.form.listType = String(this.form.listType)
            this.form.beDefault = String(this.form.beDefault)
            this.form.hasDetail = String(this.form.hasDetail)
            if (!this.form.articleTypeId) {
              this.form.articleTypeId = null
            }
            this.multiLanguageForms[currentData.languageType] = { ...this.form }
            this.activeLanguageTab = currentData.languageType
          }
        }

        this.getCmsMenuTreeselect()
        this.getArticleTypeTreeselect()
        this.open = true
        this.title = '修改菜单管理'
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      const id = row.id || this.ids
      getCmsMenu(id).then(response => {
        const sourceData = response.data

        // 检查是否有多语言关联关系
        if (sourceData.relationGroupId) {
          // 加载多语言数据
          getByRelationGroup(sourceData.relationGroupId).then(relationResponse => {
            const multiLanguageData = relationResponse.data || []

            // 为每个语言创建副本并清除ID和关系组ID
            multiLanguageData.forEach(item => {
              if (this.multiLanguageForms[item.languageType]) {
                const copyData = { ...item }
                copyData.id = null
                copyData.relationGroupId = null
                if (copyData.listType) {
                  copyData.listType = String(copyData.listType)
                }
                if (copyData.beDefault) {
                  copyData.beDefault = String(copyData.beDefault)
                }
                if (copyData.hasDetail) {
                  copyData.hasDetail = String(copyData.hasDetail)
                }
                this.$set(this.multiLanguageForms, item.languageType, copyData)
              }
            })

            this.activeLanguageTab = sourceData.languageType || 'zh_CN'
          })
        } else {
          // 只复制当前语言版本
          this.form = { ...sourceData, id: null }
          if (this.form) {
            this.form.listType = String(this.form.listType)
            this.form.beDefault = String(this.form.beDefault)
            this.form.hasDetail = String(this.form.hasDetail)
            if (!this.form.articleTypeId) {
              this.form.articleTypeId = null
            }
            this.multiLanguageForms[sourceData.languageType] = { ...this.form }
            this.activeLanguageTab = sourceData.languageType
          }
        }

        this.getCmsMenuTreeselect()
        this.getArticleTypeTreeselect()
        this.open = true
        this.title = '复制菜单管理'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 准备要提交的数据
          const dataToSave = {};

          // 添加有数据的表单
          this.supportedLanguages.forEach(lang => {
            const formData = this.multiLanguageForms[lang.value];
            if (formData && (formData.name || formData.id)) {
              // 确保特定字段为字符串类型
              if (formData.listType) {
                formData.listType = String(formData.listType)
              }
              if (formData.beDefault) {
                formData.beDefault = String(formData.beDefault)
              }
              if (formData.hasDetail) {
                formData.hasDetail = String(formData.hasDetail)
              }
              dataToSave[lang.value] = { ...formData };
            }
          });

          if (Object.keys(dataToSave).length === 0) {
            this.$modal.msgError('请至少填写一个语言版本的数据');
            return;
          }

          this.submitting = true;
          // 批量保存多语言版本
          batchSaveMultiLanguage(dataToSave).then(response => {
            this.$modal.msgSuccess('保存成功');
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError('保存失败：' + (error.msg || error.message));
          }).finally(() => {
            this.submitting = false;
          });
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('注意删除菜单，将同步删除此菜单关联的其他语言版本，是否确认删除菜单管理编号为"' + ids + '"的数据项？').then(function () {
        return delCmsMenu(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/cmsMenu/export', {
        ...this.queryParams
      }, `cmsMenu_${new Date().getTime()}.xlsx`)
    },
    /** 语言标签页点击 */
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
      // 更新表单数据
      const currentForm = this.multiLanguageForms[this.activeLanguageTab]
      if (currentForm) {
        this.form = currentForm
      }
    },
    /** 自动翻译其他语言 */
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.name) {
        this.$modal.msgError('请先填写当前语言版本的名称')
        return
      }

      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm)
        }).catch(() => { })
      } else {
        this.executeTranslation(sourceForm)
      }
    },

    /** 执行翻译 */
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateCmsMenuData(sourceForm).then(response => {
        const translatedData = response.data || {}

        // 更新多语言表单数据
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab && this.multiLanguageForms[lang]) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang].id
            const originalRelationGroupId = this.multiLanguageForms[lang].relationGroupId

            // 更新表单数据
            this.$set(this.multiLanguageForms, lang, {
              ...translatedData[lang],
              id: originalId,
              relationGroupId: originalRelationGroupId || this.currentRelationGroupId
            })

            // 转换特定字段为字符串
            if (this.multiLanguageForms[lang].listType) {
              this.multiLanguageForms[lang].listType = String(this.multiLanguageForms[lang].listType)
            }
            if (this.multiLanguageForms[lang].beDefault) {
              this.multiLanguageForms[lang].beDefault = String(this.multiLanguageForms[lang].beDefault)
            }
            if (this.multiLanguageForms[lang].hasDetail) {
              this.multiLanguageForms[lang].hasDetail = String(this.multiLanguageForms[lang].hasDetail)
            }
          }
        })

        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败: ' + error)
      }).finally(() => {
        this.translating = false
      })
    },

    /** 清空其他语言 */
    handleClearOtherLanguages() {
      this.$modal.confirm('确定要清空其他语言的数据吗？').then(() => {
        const currentLanguageCode = this.activeLanguageTab

        // 清空其他语言的数据
        this.supportedLanguages.forEach(lang => {
          if (lang.value !== currentLanguageCode) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].id : null
            const originalRelationGroupId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].relationGroupId : this.currentRelationGroupId

            // 创建新的表单数据
            this.$set(this.multiLanguageForms, lang.value, {
              id: originalId,
              parentId: this.multiLanguageForms[currentLanguageCode].parentId,
              iconUrl: null,
              position: null,
              name: null,
              routeUrl: null,
              introduction: null,
              beDefault: null,
              listType: null,
              articleTypeId: null,
              hasDetail: null,
              status: '0',
              menuLevel: this.multiLanguageForms[currentLanguageCode].menuLevel,
              orderNum: null,
              createBy: null,
              articleId: null,
              createTime: null,
              updateTime: null,
              updateBy: null,
              delFlag: null,
              beSystem: 'N',
              languageType: lang.value,
              code: null,
              relationGroupId: originalRelationGroupId,
              remark: null
            })
          }
        })

        this.$modal.msgSuccess('清空成功')
      }).catch(() => { })
    }
  }
}
</script>

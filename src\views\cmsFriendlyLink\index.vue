<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="中文友情链接" name="first"></el-tab-pane>
      <el-tab-pane label="其他语言友情链接" name="second"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="友情链接名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入友情链接名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="类型" prop="typeCode">
        <el-select v-model="queryParams.typeCode" placeholder="请选择类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.cms_friendly_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
        <el-select v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.i18N_language"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['yq:cmsFriendlyLink:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['yq:cmsFriendlyLink:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['yq:cmsFriendlyLink:remove']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['yq:cmsFriendlyLink:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cmsFriendlyLinkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="唯一标识" align="center" prop="id"/>
      <el-table-column label="友情链接名" align="center" prop="name"/>
      <el-table-column label="链接图片icon" align="center" prop="imgUrl" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="跳转地址" align="center" prop="routeUrl"/>
      <el-table-column width="200" label="语言类型" align="center" prop="languageType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType"/>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="typeCode">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.cms_friendly_type" :value="scope.row.typeCode"/>
        </template>
      </el-table-column>
      <el-table-column label="是否已禁用" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum"/>
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:cmsFriendlyLink:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleCopy(scope.row)"
            v-hasPermi="['yq:cmsFriendlyLink:edit']"
          >复制
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['yq:cmsFriendlyLink:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改友情链接对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="友情链接名" prop="name">
          <el-input v-model="form.name" placeholder="请输入友情链接名" show-word-limit/>
        </el-form-item>
        <el-form-item label="链接图片icon">
          <b-img-draggable :img-url.sync="form.imgUrl" :limit="1"></b-img-draggable>
        </el-form-item>
        <el-form-item label="语言类型" prop="languageType">
          <el-select filterable v-model="form.languageType" placeholder="请选择语言类型" clearable size="small">
            <el-option
              v-for="dict in dict.type.i18N_language"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转地址" prop="routeUrl">
          <el-input v-model="form.routeUrl" type="textarea" placeholder="请输入内容" show-word-limit/>
        </el-form-item>
        <el-form-item label="类型" prop="typeCode">
          <el-select v-model="form.typeCode" placeholder="请选择类型" clearable size="small">
            <el-option
              v-for="dict in dict.type.cms_friendly_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已禁用">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.status_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" :max="999999" :precision="0" placeholder="请输入排序"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" show-word-limit/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listCmsFriendlyLink,
  getCmsFriendlyLink,
  delCmsFriendlyLink,
  addCmsFriendlyLink,
  updateCmsFriendlyLink
} from '@/api/yq/cmsFriendlyLink'

export default {
  name: 'CmsFriendlyLink',
  dicts: ['cms_friendly_type', 'status_yes_no', 'i18N_language'],
  data() {
    return {
      activeName: 'first',
      showLanguageType: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 友情链接表格数据
      cmsFriendlyLinkList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        imgUrl: null,
        routeUrl: null,
        typeCode: null,
        name: null,
        status: null,
        orderNum: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        routeUrl: [
          { required: true, message: '跳转地址不能为空', trigger: 'blur' }
        ],
        typeCode: [
          { required: true, message: '类型不能为空', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '$comment不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '是否已禁用不能为空', trigger: 'blur' }
        ],
        languageType: [
          { required: true, message: '语言类型 不能为空', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  methods: {
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    /** 查询友情链接列表 */
    getList() {
      this.loading = true
      listCmsFriendlyLink(this.queryParams).then(response => {
        this.cmsFriendlyLinkList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        imgUrl: null,
        routeUrl: null,
        typeCode: null,
        name: null,
        status: '0',
        orderNum: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        languageType: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加友情链接'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getCmsFriendlyLink(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = '修改友情链接'
      })
    },

    /** 修改按钮操作 */
    handleCopy(row) {
      this.reset()
      const id = row.id || this.ids
      getCmsFriendlyLink(id).then(response => {
        this.form = response.data
        this.form.id = null
        this.open = true
        this.title = '修改友情链接'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateCmsFriendlyLink(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addCmsFriendlyLink(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除友情链接编号为"' + ids + '"的数据项？').then(function() {
        return delCmsFriendlyLink(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/cmsFriendlyLink/export', {
        ...this.queryParams
      }, `cmsFriendlyLink_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

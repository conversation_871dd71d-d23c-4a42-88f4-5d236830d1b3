import request from '@/utils/request'

// 查询联系我们列表
export function listContactUs(query) {
  return request({
    url: '/yq/contactUs/list',
    method: 'get',
    params: query
  })
}

// 获取菜单
export function getMenus() {
  return request({
    url: '/yq/contactUs/getMenuList',
    method: 'get',
  })
}

// 查询联系我们详细
export function getContactUs(id) {
  return request({
    url: '/yq/contactUs/detail/' + id,
    method: 'get'
  })
}

// 新增联系我们
export function addContactUs(data) {
  return request({
    url: '/yq/contactUs/add',
    method: 'post',
    data: data
  })
}

// 修改联系我们
export function updateContactUs(data) {
  return request({
    url: '/yq/contactUs/edit',
    method: 'put',
    data: data
  })
}

// 删除联系我们
export function delContactUs(id) {
  return request({
    url: '/yq/contactUs/del/' + id,
    method: 'delete'
  })
}

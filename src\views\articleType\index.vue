<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="中文类型" name="first"></el-tab-pane>
      <el-tab-pane label="其他语言类型" name="second"></el-tab-pane>
    </el-tabs>
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="88px">
      <el-form-item label="名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入名称" clearable size="small"
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
        <el-select filterable v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
          <el-option v-for="dict in (dict.type.i18N_language || [])" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否已禁用" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择是否已禁用" clearable size="small">
          <el-option v-for="dict in (dict.type.status_yes_no || [])" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['yq:articleType:add']">新增
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table height="700" v-loading="loading" :data="articleTypeList" row-key="id" default-expand-all
      :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
      <el-table-column label="名称" prop="name" />
      <el-table-column label="分类图标" align="center" prop="iconUrl" width="120">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.iconUrl" :src="scope.row.iconUrl" :width="50" :height="50" />
          <div v-else style="display: flex;flex-direction: column;align-items: center;" slot="error">
            <img style="width: 50px;height: 50px;" src="@/assets/404_images/none.png" alt="">
            <span>暂无数据</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否已禁用" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag v-if="dict.type.status_yes_no" :options="dict.type.status_yes_no" :value="scope.row.status" />
          <span v-else>{{ scope.row.status === '0' ? '正常' : '禁用' }}</span>
        </template>
      </el-table-column>
      <el-table-column width="200" label="语言类型" align="center" prop="languageType">
        <template slot-scope="scope">
          <dict-tag v-if="dict.type.i18N_language" :options="dict.type.i18N_language" :value="scope.row.languageType" />
          <span v-else>{{ scope.row.languageType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:articleType:edit']">修改
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCopy(scope.row)"
            v-hasPermi="['yq:articleType:edit']">复制
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-plus" @click="handleAdd(scope.row)"
            v-hasPermi="['yq:articleType:add']">新增
          </el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['yq:articleType:remove']"
            v-if="![10014, 10015, 10031, 10032, 15107, 15108, 10012, 10013, 10029, 10030, 15105, 15106].includes(scope.row.id)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改新闻类型对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="900px" append-to-body :close-on-click-modal="false">
      <!-- 语言标签页和按钮 -->
      <div style="position: relative; margin-bottom: 20px;">
        <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
          @tab-click="handleLanguageTabClick">
          <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label"
            :name="lang.value"></el-tab-pane>
        </el-tabs>

        <div style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px;">
          <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
            @click="handleAutoTranslate">自动翻译其他语言</el-button>
          <el-button size="mini" type="warning" icon="el-icon-delete"
            @click="handleClearOtherLanguages">清空其他语言</el-button>
        </div>
      </div>

      <!-- 表单内容 -->
      <div v-if="supportedLanguages && supportedLanguages.length > 0">
        <el-form v-if="multiLanguageForms[activeLanguageTab]" :ref="`form_${activeLanguageTab}`"
          :model="multiLanguageForms[activeLanguageTab]" :rules="rules" label-width="100px">
          <el-form-item label="名称" prop="name">
            <el-input v-model="multiLanguageForms[activeLanguageTab].name" placeholder="请输入名称"
              @input="handleFormChange(activeLanguageTab, 'name', $event)" />
          </el-form-item>
          <el-form-item label="父类分类" prop="parentId">
            <treeselect v-model="multiLanguageForms[activeLanguageTab].parentId" :options="articleTypeOptions"
              :normalizer="normalizer" placeholder="请选择父类分类"
              @input="handleFormChange(activeLanguageTab, 'parentId', $event)" />
          </el-form-item>
          <el-form-item label="分类图标">
            <b-img-draggable :img-url.sync="multiLanguageForms[activeLanguageTab].iconUrl" :limit="1"
              @change="handleFormChange(activeLanguageTab, 'iconUrl', $event)"></b-img-draggable>
          </el-form-item>
          <el-form-item label="是否已禁用">
            <el-radio-group v-model="multiLanguageForms[activeLanguageTab].status"
              @input="handleFormChange(activeLanguageTab, 'status', $event)">
              <el-radio v-for="dict in (dict.type.status_yes_no || [])" :key="dict.value" :label="dict.value">{{
                dict.label
                }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="排序" prop="orderNum">
            <el-input-number v-model="multiLanguageForms[activeLanguageTab].orderNum" :min="1" :max="999999"
              :precision="0" placeholder="请输入排序" @input="handleFormChange(activeLanguageTab, 'orderNum', $event)" />
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="multiLanguageForms[activeLanguageTab].remark" type="textarea" placeholder="请输入内容"
              @input="handleFormChange(activeLanguageTab, 'remark', $event)" />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listArticleType,
  getArticleType,
  delArticleType,
  addArticleType,
  updateArticleType,
  autoTranslateArticleTypeData,
  batchSaveMultiLanguage,
  getByRelationGroup
} from '@/api/yq/articleType'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'ArticleType',
  dicts: ['status_yes_no', 'i18N_language'],
  components: {
    Treeselect
  },
  data() {
    return {
      activeName: 'first',
      showLanguageType: false,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 新闻类型表格数据
      articleTypeList: [],
      // 新闻类型树选项
      articleTypeOptions: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        name: null,
        languageType: null,
        parentId: null,
        typeCode: 1,
        typeLevel: null,
        iconUrl: null,
        ancestors: null,
        status: null,
        orderNum: null,
        delFlag: 0
      },
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前活跃的语言标签页
      activeLanguageTab: 'zh_CN',
      // 翻译状态
      translating: false,
      // 提交状态
      submitting: false,
      // 当前编辑的关系组ID
      currentRelationGroupId: null,
      // 是否已经翻译过(用于判断是否需要二次确认)
      hasTranslated: false,
      // 表单校验
      rules: {
        name: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 从字典中获取支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  created() {
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  mounted() {
    // 等待字典数据加载完成后再初始化
    this.$nextTick(() => {
      this.waitForDictAndInitialize()
    })
  },
  methods: {
    // 等待字典数据加载并初始化
    waitForDictAndInitialize() {
      // 检查字典数据是否已加载
      if (this.dict && this.dict.type && this.dict.type.i18N_language && this.dict.type.status_yes_no) {
        this.initializeDefaultLanguage()
      } else {
        // 如果字典数据还未加载，延迟重试
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 50)
      }
    },
    // 初始化默认语言
    initializeDefaultLanguage() {
      const languages = this.dict.type.i18N_language
      // 优先选择中文，如果没有则选择第一个
      const zhCN = languages.find(lang => lang.value === 'zh_CN')
      this.activeLanguageTab = zhCN ? 'zh_CN' : languages[0].value
      this.initializeMultiLanguageForms()
    },
    // 初始化多语言表单
    initializeMultiLanguageForms() {
      const languages = this.dict.type.i18N_language
      languages.forEach(lang => {
        if (!this.multiLanguageForms[lang.value]) {
          this.$set(this.multiLanguageForms, lang.value, {
            id: null,
            name: null,
            typeCode: 1,
            parentId: null,
            typeLevel: null,
            iconUrl: null,
            ancestors: null,
            status: '0',
            orderNum: null,
            createBy: null,
            createTime: null,
            updateTime: null,
            updateBy: null,
            delFlag: null,
            languageType: lang.value,
            remark: null
          })
        }
      })
    },
    handleClick(tab, event) {
      if (tab.index == 0) {
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    /** 查询新闻类型列表 */
    getList() {
      this.loading = true
      listArticleType(this.queryParams).then(response => {
        this.articleTypeList = this.handleTree(response.data, 'id', 'parentId')
        this.loading = false
      }).catch(error => {
        console.error('获取新闻类型列表失败:', error)
        this.loading = false
        this.$modal.msgError('获取列表数据失败')
      })
    },
    /** 转换新闻类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        label: node.name,
        children: node.children
      }
    },
    /** 查询新闻类型下拉树结构 */
    getTreeselect() {
      listArticleType({ typeCode: 1, delFlag: 0 }).then(response => {
        this.articleTypeOptions = []
        const data = { id: 0, name: '顶级节点', children: [] }
        data.children = this.handleTree(response.data, 'id', 'parentId')
        this.articleTypeOptions.push(data)
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      // 重置多语言表单数据
      this.multiLanguageForms = {}
      this.currentRelationGroupId = null
      // 重置翻译状态
      this.hasTranslated = false
      // 确保字典数据可用后再初始化
      if (this.dict && this.dict.type && this.dict.type.i18N_language) {
        this.initializeMultiLanguageForms()
        this.initializeDefaultLanguage()
      }
      // 重置所有表单验证
      if (this.supportedLanguages && this.supportedLanguages.length > 0) {
        this.supportedLanguages.forEach(lang => {
          this.$nextTick(() => {
            if (this.$refs[`form_${lang.value}`] && this.$refs[`form_${lang.value}`][0]) {
              this.$refs[`form_${lang.value}`][0].clearValidate()
            }
          })
        })
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()
      this.getTreeselect()
      const parentId = (row != null && row.id) ? row.id : 0
      // 为所有语言版本设置相同的父级ID
      if (this.supportedLanguages && this.supportedLanguages.length > 0) {
        this.supportedLanguages.forEach(lang => {
          if (this.multiLanguageForms[lang.value]) {
            this.multiLanguageForms[lang.value].parentId = parentId
          }
        })
      }
      this.open = true
      this.title = '添加新闻类型'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getTreeselect()

      // 获取当前记录的详细信息
      getArticleType(row.id).then(response => {
        const currentData = response.data

        // 检查是否有多语言关联关系
        if (currentData.relationGroupId) {
          this.currentRelationGroupId = currentData.relationGroupId
          // 获取所有语言版本
          this.loadMultiLanguageData(currentData.relationGroupId)
        } else {
          // 没有关联关系，只加载当前语言版本
          this.multiLanguageForms[currentData.languageType] = { ...currentData }
          this.activeLanguageTab = currentData.languageType
        }

        this.open = true
        this.title = '修改新闻类型'
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      this.getTreeselect()

      getArticleType(row.id).then(response => {
        const sourceData = response.data
        // 复制到中文版本，清除ID
        this.multiLanguageForms['zh_CN'] = {
          ...sourceData,
          id: null,
          languageType: 'zh_CN'
        }
        this.activeLanguageTab = 'zh_CN'
        this.open = true
        this.title = '复制新闻类型'
      })
    },
    /** 提交按钮 */
    submitForm() {
      // 验证所有有数据的表单
      const formsToValidate = []
      const dataToSave = {}

      if (this.supportedLanguages && this.supportedLanguages.length > 0) {
        this.supportedLanguages.forEach(lang => {
          const formData = this.multiLanguageForms[lang.value]
          if (formData && (formData.name || formData.id)) {
            formsToValidate.push(`form_${lang.value}`)
            dataToSave[lang.value] = { ...formData }
          }
        })
      }

      if (Object.keys(dataToSave).length === 0) {
        this.$modal.msgError('请至少填写一个语言版本的数据')
        return
      }

      // 新增页面且未翻译的二次提醒
      console.log('提交检查:', {
        title: this.title,
        hasTranslated: this.hasTranslated,
        dataToSaveLength: Object.keys(dataToSave).length
      })

      if (this.title === '添加新闻类型' && !this.hasTranslated) {
        this.$modal.confirm('当前未翻译其他语言，是否只保存当前一个语言版本？', '提示', {
          confirmButtonText: '确认保存',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.executeSubmit(formsToValidate, dataToSave)
        }).catch(() => {
          // 用户取消，不执行任何操作
        })
        return
      }

      // 直接执行提交
      this.executeSubmit(formsToValidate, dataToSave)
    },

    // 执行提交的具体逻辑
    executeSubmit(formsToValidate, dataToSave) {
      // 验证表单
      let validationPromises = []
      formsToValidate.forEach(formRef => {
        if (this.$refs[formRef] && this.$refs[formRef][0]) {
          validationPromises.push(
            new Promise((resolve, reject) => {
              this.$refs[formRef][0].validate(valid => {
                if (valid) {
                  resolve()
                } else {
                  reject(new Error(`${formRef} validation failed`))
                }
              })
            })
          )
        }
      })

      Promise.all(validationPromises).then(() => {
        this.submitting = true
        // 批量保存多语言版本
        batchSaveMultiLanguage(dataToSave).then(response => {
          this.$modal.msgSuccess('保存成功')
          this.open = false
          this.getList()
        }).catch(error => {
          this.$modal.msgError('保存失败：' + (error.msg || error.message))
        }).finally(() => {
          this.submitting = false
        })
      }).catch(error => {
        this.$modal.msgError('请检查表单填写是否正确')
      })
    },
    /** 加载多语言数据 */
    loadMultiLanguageData(relationGroupId) {
      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []
        // 清空现有数据
        this.multiLanguageForms = {}
        this.initializeMultiLanguageForms()

        // 填充已有的多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.multiLanguageForms[item.languageType] = { ...item }
          }
        })

        // 设置默认激活的tab为中文
        this.activeLanguageTab = 'zh_CN'
      }).catch(error => {
        this.$modal.msgError('加载多语言数据失败：' + (error.msg || error.message))
      })
    },

    /** 自动翻译 */
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.name) {
        this.$modal.msgError('请先填写当前语言版本的名称')
        return
      }

      // 判断是否需要二次确认
      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm);
        }).catch(() => { });
      } else {
        // 首次翻译，直接执行
        this.executeTranslation(sourceForm);
      }
    },

    // 执行翻译的实际逻辑
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateArticleTypeData(sourceForm).then(response => {
        const translatedData = response.data || {}
        // 更新其他语言版本的翻译内容
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab) {
            // 确保目标语言的表单对象存在
            if (!this.multiLanguageForms[lang]) {
              this.$set(this.multiLanguageForms, lang, {
                id: null,
                name: null,
                typeCode: 1,
                parentId: this.multiLanguageForms[this.activeLanguageTab].parentId,
                typeLevel: null,
                iconUrl: null,
                ancestors: null,
                status: '0',
                orderNum: null,
                createBy: null,
                createTime: null,
                updateTime: null,
                updateBy: null,
                delFlag: null,
                languageType: lang,
                remark: null
              })
            }
            // 保留原有的ID和其他系统字段，只更新翻译字段
            this.multiLanguageForms[lang] = {
              ...this.multiLanguageForms[lang],
              ...translatedData[lang],
              languageType: lang
            }
          }
        })

        // 标记已经翻译过
        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败：' + (error.msg || error.message))
      }).finally(() => {
        this.translating = false
      })
    },

    /** 清空其他语言版本 */
    handleClearOtherLanguages() {
      this.$modal.confirm('确认清空其他语言版本的数据吗？').then(() => {
        if (this.supportedLanguages && this.supportedLanguages.length > 0) {
          this.supportedLanguages.forEach(lang => {
            if (lang.value !== this.activeLanguageTab) {
              this.multiLanguageForms[lang.value] = {
                id: null,
                name: null,
                typeCode: 1,
                parentId: this.multiLanguageForms[this.activeLanguageTab].parentId,
                typeLevel: null,
                iconUrl: null,
                ancestors: null,
                status: '0',
                orderNum: null,
                createBy: null,
                createTime: null,
                updateTime: null,
                updateBy: null,
                delFlag: null,
                languageType: lang.value,
                remark: null
              }
            }
          })
        }
        this.$modal.msgSuccess('已清空其他语言版本')
      }).catch(() => { })
    },

    /** 语言标签页切换处理 */
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
    },

    /** 表单变化处理 */
    handleFormChange(lang, field, value) {
      // 可以在这里添加表单变化的处理逻辑
      // 比如标记表单已修改等
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('注意删除分类，将同步删除此数据关联的其他语言分类，是否确认删除新闻类型编号为"' + row.id + '"的数据项？').then(function () {
        return delArticleType(row.id)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    }
  }
}
</script>

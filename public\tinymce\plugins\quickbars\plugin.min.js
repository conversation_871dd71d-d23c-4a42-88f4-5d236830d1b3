/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.1 (2019-10-28)
 */
!function(t){"use strict";function e(){}function l(e){return function(){return e}}function n(){return h}var r,o=tinymce.util.Tools.resolve("tinymce.PluginManager"),i=0,u=function(e,n,r){e.plugins.table?e.plugins.table.insertTable(n,r):function(r,t,o){r.undoManager.transact(function(){var e,n;r.insertContent(function(e,n){var r,t,o;for(o='<table data-mce-id="mce" style="width: 100%">',o+="<tbody>",t=0;t<n;t++){for(o+="<tr>",r=0;r<e;r++)o+="<td><br></td>";o+="</tr>"}return o+="</tbody>",o+="</table>"}(t,o)),(e=function(e){return e.dom.select("*[data-mce-id]")[0]}(r)).removeAttribute("data-mce-id"),n=r.dom.select("td,th",e),r.selection.setCursorLocation(n[0],0)})}(e,n,r)},c=function(e,n,r){var t,o;o=(t=e.editorUpload.blobCache).create(function(e){var n=(new Date).getTime();return e+"_"+Math.floor(1e9*Math.random())+ ++i+String(n)}("mceu"),r,n),t.add(o),e.insertContent(e.dom.createHTML("img",{src:o.blobUri()}))},a=tinymce.util.Tools.resolve("tinymce.util.Promise"),s=function(r){return new a(function(e){var n=new t.FileReader;n.onloadend=function(){e(n.result.split(",")[1])},n.readAsDataURL(r)})},f=function(){return new a(function(n){var e;(e=t.document.createElement("input")).type="file",e.style.position="fixed",e.style.left=0,e.style.top=0,e.style.opacity=.001,t.document.body.appendChild(e),e.onchange=function(e){n(Array.prototype.slice.call(e.target.files))},e.click(),e.parentNode.removeChild(e)})},d=function(r){r.ui.registry.addButton("quickimage",{icon:"image",tooltip:"Insert image",onAction:function(){f().then(function(e){var n=e[0];s(n).then(function(e){c(r,e,n)})})}}),r.ui.registry.addButton("quicktable",{icon:"table",tooltip:"Insert table",onAction:function(){u(r,2,2)}})},m=l(!1),g=l(!0),h=(r={fold:function(e,n){return e()},is:m,isSome:m,isNone:g,getOr:N,getOrThunk:p,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:l(null),getOrUndefined:l(undefined),or:N,orThunk:p,map:n,each:e,bind:n,exists:m,forall:g,filter:n,equals:v,equals_:v,toArray:function(){return[]},toString:l("none()")},Object.freeze&&Object.freeze(r),r);function v(e){return e.isNone()}function p(e){return e()}function N(e){return e}function O(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}var b=function(r){function e(){return o}function n(e){return e(r)}var t=l(r),o={fold:function(e,n){return n(r)},is:function(e){return r===e},isSome:g,isNone:m,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:e,orThunk:e,map:function(e){return b(e(r))},each:function(e){e(r)},bind:n,exists:n,forall:n,filter:function(e){return e(r)?o:h},toArray:function(){return[r]},toString:function(){return"some("+r+")"},equals:function(e){return e.is(r)},equals_:function(e,n){return e.fold(m,function(e){return n(r,e)})}};return o},w={some:b,none:n,from:function(e){return null===e||e===undefined?h:b(e)}},E=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:l(e)}},T={fromHtml:function(e,n){var r=(n||t.document).createElement("div");if(r.innerHTML=e,!r.hasChildNodes()||1<r.childNodes.length)throw t.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return E(r.childNodes[0])},fromTag:function(e,n){var r=(n||t.document).createElement(e);return E(r)},fromText:function(e,n){var r=(n||t.document).createTextNode(e);return E(r)},fromDom:E,fromPoint:function(e,n,r){var t=e.dom();return w.from(t.elementFromPoint(n,r)).map(E)}},S=(t.Node.ATTRIBUTE_NODE,t.Node.CDATA_SECTION_NODE,t.Node.COMMENT_NODE,t.Node.DOCUMENT_NODE,t.Node.DOCUMENT_TYPE_NODE,t.Node.DOCUMENT_FRAGMENT_NODE,t.Node.ELEMENT_NODE),x=(t.Node.TEXT_NODE,t.Node.PROCESSING_INSTRUCTION_NODE,t.Node.ENTITY_REFERENCE_NODE,t.Node.ENTITY_NODE,t.Node.NOTATION_NODE,"undefined"!=typeof t.window?t.window:Function("return this;")(),O("string")),y=O("object"),k=O("array"),D=O("boolean"),_=O("undefined"),C=O("function"),A=Array.prototype.slice;C(Array.from)&&Array.from;function M(e,n,r,t,o){return e(r,t)?w.some(r):C(o)&&o(r)?w.none():n(r,t,o)}function R(e,n,r){return 0!=(e.compareDocumentPosition(n)&r)}function I(e,n){var r=function(e,n){for(var r=0;r<e.length;r++){var t=e[r];if(t.test(n))return t}return undefined}(e,n);if(!r)return{major:0,minor:0};function t(e){return Number(n.replace(r,"$"+e))}return Y(t(1),t(2))}function q(e,n){return function(){return n===e}}function L(e,n){return function(){return n===e}}function P(e,n){var r=String(n).toLowerCase();return function(e,n){for(var r=0,t=e.length;r<t;r++){var o=e[r];if(n(o,r))return w.some(o)}return w.none()}(e,function(e){return e.search(r)})}function F(e,n){return-1!==e.indexOf(n)}function U(n){return function(e){return F(e,n)}}function B(e,n){var r=e.dom();if(r.nodeType!==de)return!1;var t=r;if(t.matches!==undefined)return t.matches(n);if(t.msMatchesSelector!==undefined)return t.msMatchesSelector(n);if(t.webkitMatchesSelector!==undefined)return t.webkitMatchesSelector(n);if(t.mozMatchesSelector!==undefined)return t.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function j(e,n,r){for(var t=e.dom(),o=C(r)?r:l(!1);t.parentNode;){t=t.parentNode;var i=T.fromDom(t);if(n(i))return w.some(i);if(o(i))break}return w.none()}function H(e,n,r){return j(e,function(e){return B(e,n)},r)}var X,z=function(e,n){return R(e,n,t.Node.DOCUMENT_POSITION_CONTAINED_BY)},G=function(e){function n(){return r}var r=e;return{get:n,set:function(e){r=e},clone:function(){return G(n())}}},W=function(){return Y(0,0)},Y=function(e,n){return{major:e,minor:n}},$={nu:Y,detect:function(e,n){var r=String(n).toLowerCase();return 0===e.length?W():I(e,r)},unknown:W},V="Firefox",J=function(e){var n=e.current;return{current:n,version:e.version,isEdge:q("Edge",n),isChrome:q("Chrome",n),isIE:q("IE",n),isOpera:q("Opera",n),isFirefox:q(V,n),isSafari:q("Safari",n)}},K={unknown:function(){return J({current:undefined,version:$.unknown()})},nu:J,edge:l("Edge"),chrome:l("Chrome"),ie:l("IE"),opera:l("Opera"),firefox:l(V),safari:l("Safari")},Q="Windows",Z="Android",ee="Solaris",ne="FreeBSD",re=function(e){var n=e.current;return{current:n,version:e.version,isWindows:L(Q,n),isiOS:L("iOS",n),isAndroid:L(Z,n),isOSX:L("OSX",n),isLinux:L("Linux",n),isSolaris:L(ee,n),isFreeBSD:L(ne,n)}},te={unknown:function(){return re({current:undefined,version:$.unknown()})},nu:re,windows:l(Q),ios:l("iOS"),android:l(Z),linux:l("Linux"),osx:l("OSX"),solaris:l(ee),freebsd:l(ne)},oe=function(e,r){return P(e,r).map(function(e){var n=$.detect(e.versionRegexes,r);return{current:e.name,version:n}})},ie=function(e,r){return P(e,r).map(function(e){var n=$.detect(e.versionRegexes,r);return{current:e.name,version:n}})},ue=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ce=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return F(e,"edge/")&&F(e,"chrome")&&F(e,"safari")&&F(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,ue],search:function(e){return F(e,"chrome")&&!F(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return F(e,"msie")||F(e,"trident")}},{name:"Opera",versionRegexes:[ue,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:U("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:U("firefox")},{name:"Safari",versionRegexes:[ue,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return(F(e,"safari")||F(e,"mobile/"))&&F(e,"applewebkit")}}],ae=[{name:"Windows",search:U("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return F(e,"iphone")||F(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:U("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:U("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:U("linux"),versionRegexes:[]},{name:"Solaris",search:U("sunos"),versionRegexes:[]},{name:"FreeBSD",search:U("freebsd"),versionRegexes:[]}],se={browsers:l(ce),oses:l(ae)},fe=G(function(e,n){var r=se.browsers(),t=se.oses(),o=oe(r,e).fold(K.unknown,K.nu),i=ie(t,e).fold(te.unknown,te.nu);return{browser:o,os:i,deviceType:function(e,n,r,t){var o=e.isiOS()&&!0===/ipad/i.test(r),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),c=u||t("(pointer:coarse)"),a=o||!i&&u&&t("(min-device-width:768px)"),s=i||u&&!a,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),d=!s&&!a&&!f;return{isiPad:l(o),isiPhone:l(i),isTablet:l(a),isPhone:l(s),isTouch:l(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:l(f),isDesktop:l(d)}}(i,o,e,n)}}(t.navigator.userAgent,function(e){return t.window.matchMedia(e).matches})),de=S,le=(fe.get().browser.isIE(),{getToolbarItemsOr:(X=x,function(e,n,r){return function(e,n){if(!n(e))throw new Error("Default value doesn't match requested type.")}(r,X),function(e,n){if(k(e)||y(e))throw new Error("expected a string but found: "+e);return _(e)?n:D(e)?!1===e?"":n:e}(e.getParam(n,r),r)})}),me=function(e){return le.getToolbarItemsOr(e,"quickbars_selection_toolbar","bold italic | quicklink h2 h3 blockquote")},ge=function(e){return le.getToolbarItemsOr(e,"quickbars_insert_toolbar","quickimage quicktable")},he=function(o){var e=ge(o);0<e.trim().length&&o.ui.registry.addContextToolbar("quickblock",{predicate:function(e){function n(e){return e.dom()===o.getBody()}var r=T.fromDom(e),t=o.schema.getTextBlockElements();return function(e,n,r){return M(B,H,e,n,r)}(r,"table",n).fold(function(){return function(e,n,r){return M(function(e,n){return n(e)},j,e,n,r)}(r,function(e){return function(e){return e.dom().nodeName.toLowerCase()}(e)in t&&o.dom.isEmpty(e.dom())},n).isSome()},function(){return!1})},items:e,position:"line",scope:"editor"})},ve=function(n){n.ui.registry.addContextToolbar("imageselection",{predicate:function(e){return"IMG"===e.nodeName||"FIGURE"===e.nodeName&&/image/i.test(e.className)},items:"alignleft aligncenter alignright",position:"node"});var e=me(n);0<e.trim().length&&n.ui.registry.addContextToolbar("textselection",{predicate:function(e){return!n.selection.isCollapsed()},items:e,position:"selection"})};!function pe(){o.add("quickbars",function(e){d(e),he(e),ve(e)})}()}(window);
<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <!-- 基本信息 -->
      <el-form-item label="标题" prop="title">
        <el-input v-model="form.title" placeholder="请输入标题" />
      </el-form-item>
      
      <el-form-item label="描述" prop="description">
        <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入描述" />
      </el-form-item>
      
      <el-form-item label="内容" prop="content">
        <el-input v-model="form.content" type="textarea" :rows="6" placeholder="请输入内容" />
      </el-form-item>
      
      <el-form-item label="语言类型" prop="languageType">
        <el-select v-model="form.languageType" placeholder="请选择语言类型">
          <el-option label="简体中文" value="zh_CN" />
          <el-option label="繁体中文" value="zh_TW" />
          <el-option label="繁体中文(香港)" value="zh_HK" />
          <el-option label="英语(美国)" value="en_US" />
          <el-option label="英语(英国)" value="en_GB" />
        </el-select>
      </el-form-item>
      
      <!-- 自动翻译组件 -->
      <el-form-item>
        <auto-translate
          :entity-id="form.id"
          entity-type="article"
          :original-data="form"
          :can-translate="canAutoTranslate"
          @translation-confirmed="handleTranslationConfirmed"
        />
      </el-form-item>
      
      <!-- 操作按钮 -->
      <el-form-item>
        <el-button type="primary" @click="submitForm">保存</el-button>
        <el-button @click="cancel">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import AutoTranslate from '@/components/AutoTranslate'

export default {
  name: 'ArticleForm',
  components: {
    AutoTranslate
  },
  data() {
    return {
      form: {
        id: null,
        title: '',
        description: '',
        content: '',
        languageType: 'zh_CN'
      },
      rules: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        languageType: [
          { required: true, message: '语言类型不能为空', trigger: 'change' }
        ]
      },
      translationData: [] // 存储翻译结果
    }
  },
  computed: {
    // 判断是否可以进行自动翻译
    canAutoTranslate() {
      return this.form.title && this.form.title.trim() !== '';
    }
  },
  methods: {
    // 处理翻译确认
    handleTranslationConfirmed(translationResults) {
      this.translationData = translationResults;
      this.$message.success('翻译结果已确认，将在保存时一并提交');
    },
    
    // 提交表单
    async submitForm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          try {
            const submitData = {
              ...this.form,
              translationData: this.translationData // 包含翻译数据
            };
            
            let response;
            if (this.form.id) {
              // 编辑
              response = await this.$http.put('/yq/article', submitData);
            } else {
              // 新增
              response = await this.$http.post('/yq/article', submitData);
            }
            
            if (response.code === 200) {
              this.$message.success('保存成功');
              this.$router.push('/yq/article');
            } else {
              this.$message.error(response.msg || '保存失败');
            }
          } catch (error) {
            console.error('保存错误:', error);
            this.$message.error('保存异常，请稍后重试');
          }
        }
      });
    },
    
    // 取消
    cancel() {
      this.$router.push('/yq/article');
    }
  }
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}
</style>
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="联系方式" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入联系方式"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="地点" prop="address">
        <el-input
          v-model="queryParams.address"
          placeholder="请输入地点"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否已禁用" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable size="small">
          <el-option
            v-for="dict in dict.type.status_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="语言类型" prop="languageType">
        <el-select v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
          <el-option
            v-for="dict in dict.type.i18N_language"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['yq:contactUs:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['yq:contactUs:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['yq:contactUs:remove']"
        >删除
        </el-button>
      </el-col>
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          type="warning"-->
      <!--          plain-->
      <!--          icon="el-icon-download"-->
      <!--          size="mini"-->
      <!--          @click="handleExport"-->
      <!--          v-hasPermi="['yq:contactUs:export']"-->
      <!--        >导出-->
      <!--        </el-button>-->
      <!--      </el-col>-->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="contactUsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column width="200" label="省" align="center" prop="province"/>
      <el-table-column width="200" label="标题" align="center" prop="title"/>
      <el-table-column show-overflow-tooltip width="150" label="cms菜单" align="center" prop="menuName"/>
      <el-table-column label="图片" align="center" prop="imageUrl" width="100">
        <template slot-scope="scope">
          <image-preview v-if="scope.row.imageUrl" :src="scope.row.imageUrl" :width="50" :height="50"/>
          <div v-else style="display: flex;flex-direction: column;align-items: center;" slot="error">
            <img style="width: 50px;height: 50px;" src="@/assets/404_images/none.png" alt="">
            <span>暂无数据</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip width="200" label="简介" align="center" prop="description"/>
      <el-table-column width="120" label="联系方式" align="center" prop="phone"/>
      <el-table-column width="120" label="地点" align="center" prop="address"/>
      <el-table-column width="120" label="传真" align="center" prop="fax"/>
      <el-table-column width="120" label="邮编" align="center" prop="postCode"/>
      <el-table-column width="120" label="电子邮箱" align="center" prop="email"/>
      <el-table-column width="120" label="是否已禁用" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum"/>
      <el-table-column width="120" label="语言类型" align="center" prop="languageType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType"/>
        </template>
      </el-table-column>
      <el-table-column width="150" show-overflow-tooltip label="备注" align="center" prop="remark"/>
      <el-table-column fixed="right" width="120" label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:contactUs:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['yq:contactUs:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改联系我们对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="标题" prop="title">
          <el-input maxlength="50" v-model="form.title" placeholder="请输入标题" show-word-limit/>
        </el-form-item>
        <el-form-item label="省" prop="province">
          <el-input maxlength="50" v-model="form.province" placeholder="请输入省" show-word-limit/>
        </el-form-item>
        <el-form-item label="cms菜单" prop="cmsMenuId">
          <treeselect v-model="form.cmsMenuId" :options="menus" :default-expand-level="1"
                      :normalizer="normalizer"
                      placeholder="请选择cms菜单"
          >
            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                   :class="labelClassName"
            >
              {{ node.label }} ： {{ node.raw.language }}
              <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
            </label>
          </treeselect>
        </el-form-item>
        <el-form-item label="图片">
          <b-img-draggable :img-url.sync="form.imageUrl" :limit="1"></b-img-draggable>
        </el-form-item>
        <el-form-item label="语言类型" prop="languageType">
          <el-select disabled v-model="form.languageType" placeholder="请先选择cms菜单">
            <el-option
              v-for="dict in dict.type.i18N_language"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="简介" prop="description">
          <el-input :rows="6" maxlength="200" type="textarea" v-model="form.description" placeholder="请输入简介"
                    show-word-limit
          />
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="form.phone" placeholder="请输入联系方式" show-word-limit/>
        </el-form-item>
        <el-form-item label="地点" prop="address">
          <el-input v-model="form.address" placeholder="请输入地点" show-word-limit/>
        </el-form-item>
        <el-form-item label="传真" prop="fax">
          <el-input v-model="form.fax" placeholder="请输入传真" show-word-limit/>
        </el-form-item>
        <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="form.email" placeholder="请输入电子邮箱" show-word-limit/>
        </el-form-item>
        <el-form-item label="邮编" prop="postCode">
          <el-input v-model="form.postCode" placeholder="请输入邮编" show-word-limit/>
        </el-form-item>
        <el-form-item label="是否禁用" prop="status">
          <el-select v-model="form.status" placeholder="请选择是否禁用">
            <el-option
              v-for="dict in dict.type.status_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" :max="999999" :precision="0" placeholder="请输入排序"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input maxlength="50" v-model="form.remark" placeholder="请输入备注" show-word-limit/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listContactUs, getContactUs, delContactUs, addContactUs, updateContactUs } from '@/api/yq/contactUs'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import { cmsMenuTreeselect, getCmsMenu } from '@/api/yq/cmsMenu'

export default {
  name: 'ContactUs',
  components: {
    Treeselect
  },
  dicts: ['status_yes_no', 'i18N_language'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      cmsMenu: {},
      // 联系我们表格数据
      contactUsList: [],
      menus: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        cmsMenuId: null,
        phone: null,
        address: null,
        status: null,
        orderNum: null,
        languageType: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: '标题不能为空', trigger: 'blur' }
        ],
        cmsMenuId: [
          { required: true, message: 'cms菜单不能为空', trigger: 'change' }
        ],
        phone: [
          { required: true, message: '联系方式不能为空', trigger: 'blur' }
        ],
        address: [
          { required: true, message: '地点不能为空', trigger: 'blur' }
        ],
        fax: [
          { required: true, message: '传真不能为空', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '电子邮箱不能为空', trigger: 'blur' }
        ],
        postCode: [
          { required: true, message: '邮编不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  watch: {
    'form.cmsMenuId'(val) {
      if (val) {
        getCmsMenu(val).then(response => {
          let info = response.data
          if (info) {
            this.form.languageType = info.languageType
          }
        })
      } else {
        this.form.languageType = null
      }
    }
  },
  methods: {

    /** 转换新闻类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        name: node.label,
        children: node.children
      }
    },

    /**
     * 获取联系我们菜单
     */
    getMenus() {
      cmsMenuTreeselect().then(response => {
        this.menus = response.data
      })
    },

    /** 查询联系我们列表 */
    getList() {
      this.loading = true
      listContactUs(this.queryParams).then(response => {
        this.contactUsList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.cmsMenu = {}
      this.form = {
        id: null,
        title: null,
        cmsMenuId: null,
        description: null,
        phone: null,
        address: null,
        fax: null,
        email: null,
        postCode: null,
        imageUrl: null,
        status: null,
        orderNum: null,
        languageType: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        province: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getMenus()
      this.open = true
      this.title = '添加联系我们'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getMenus()
      const id = row.id || this.ids
      getContactUs(id).then(response => {
        this.form = response.data
        this.cmsMenu = {}
        this.cmsMenu.value = this.form.cmsMenuId
        this.cmsMenu.label = this.form.menuName
        this.cmsMenu.language = this.form.languageType
        this.open = true
        this.title = '修改联系我们'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateContactUs(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addContactUs(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除联系我们编号为"' + ids + '"的数据项？').then(function() {
        return delContactUs(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/contactUs/export', {
        ...this.queryParams
      }, `contactUs_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

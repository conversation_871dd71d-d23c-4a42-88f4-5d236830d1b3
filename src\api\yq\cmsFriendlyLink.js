import request from '@/utils/request'

// 查询友情链接列表
export function listCmsFriendlyLink(query) {
  return request({
    url: '/yq/cmsFriendlyLink/list',
    method: 'get',
    params: query
  })
}

// 查询友情链接详细
export function getCmsFriendlyLink(id) {
  return request({
    url: '/yq/cmsFriendlyLink/detail/' + id,
    method: 'get'
  })
}

// 新增友情链接
export function addCmsFriendlyLink(data) {
  return request({
    url: '/yq/cmsFriendlyLink/add',
    method: 'post',
    data: data
  })
}

// 修改友情链接
export function updateCmsFriendlyLink(data) {
  return request({
    url: '/yq/cmsFriendlyLink/edit',
    method: 'put',
    data: data
  })
}

// 删除友情链接
export function delCmsFriendlyLink(id) {
  return request({
    url: '/yq/cmsFriendlyLink/del/' + id,
    method: 'delete'
  })
}

<template>
  <div class="upload-container">
    <el-button :style="{background:color,borderColor:color}"  icon="el-icon-upload" size="mini" type="primary" @click=" btnClick">
      上传PDF
    </el-button>
    <section style="display: none;">
      <el-upload
        :file-list="fileList"
        :show-file-list="true"
        list-type="picture-card"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        :action="baseUpload"
        :data="tokens"
      >
        <el-button size="small" ref="toSelect"  type="primary">
          选择文件
        </el-button>
      </el-upload>
      <el-button @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确认
      </el-button>
    </section>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
//import { upload } from '../../../api/upload'

export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#1890ff'
    }
  },
  data() {
    return {
      baseUpload: '',
      baseFileUrl: '',
      tokens: {},
      dialogVisible: false,
      listObj: {},
      fileList: []
    }
  },
  methods: {
    btnClick() {
      this.$refs.toSelect.$el.click()
    },
    checkAllSuccess() {
      return Object.keys(this.listObj).every(item => this.listObj[item].hasSuccess)
    },
    handleSubmit(name) {
      const arr = Object.keys(this.listObj).map(v => this.listObj[v])
      if (!this.checkAllSuccess()) {
        this.$message('Please wait for all images to be uploaded successfully. If there is a network problem, please refresh the page and upload again!')
        return
      }
      this.$emit('successCBK', arr,name)
      this.listObj = {}
      this.fileList = []
      this.dialogVisible = false
    },
    handleSuccess(response, file) {
      const uid = file.uid
      const objKeyArr = Object.keys(this.listObj)
      const url = this.baseFileUrl + '/' + this.tokens.key
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url = url
          this.listObj[objKeyArr[i]].hasSuccess = true
          break;
        }
      }
      this.handleSubmit(file.name)
    },
    handleRemove(file) {
      const uid = file.uid
      const objKeyArr = Object.keys(this.listObj)
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          delete this.listObj[objKeyArr[i]]
          return
        }
      }
    },
    beforeUpload(file) {
      if(file.type.indexOf('pdf')=='-1'){
         this.$message.error('您选择的是 .pdf 格式!');
         return
      }
      const _self = this
      const fileName = file.uid
      this.listObj[fileName] = {}
      // return new Promise((resolve, reject) => {
      //   upload({ value: file.lastModified + file.name }).then(res => {
      //     this.tokens.key = res.data.key
      //     this.tokens.OSSAccessKeyId = res.data.params.OSSAccessKeyId
      //     this.tokens.policy = res.data.params.policy
      //     this.tokens.signature = res.data.params.signature
      //     this.tokens.success_action_status = res.data.params.success_action_status
      //     _self.listObj[fileName] = { hasSuccess: false, uid: file.uid }
      //     resolve(true)
      //   }, _ => {
      //     reject(false)
      //   })
      // })
      // return new Promise((resolve, reject) => {
      //   const img = new Image()
      //   img.src = _URL.createObjectURL(file)
      //   img.onload = function() {
      //     _self.listObj[fileName] = { hasSuccess: false, uid: file.uid, width: this.width, height: this.height }
      //   }
      //   resolve(true)
      // })
    }
  }
}
</script>

<style lang="scss" scoped>
// .editor-slide-upload {
//   margin-bottom: 20px;
//   /deep/ .el-upload--picture-card {
//     width: 100%;
//   }
// }
</style>

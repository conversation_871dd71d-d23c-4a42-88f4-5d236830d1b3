<template>
  <div class="app-container">

    <el-row :gutter="20">
      <!--分类数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="typeName" placeholder="请输入分类名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="typeOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" :highlight-current="true" ref="tree" default-expand-all
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--文章数据-->
      <el-col :span="20" :xs="24">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="中文文章" name="first"></el-tab-pane>
          <el-tab-pane label="其他语言文章" name="second"></el-tab-pane>
        </el-tabs>

        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="文字标题" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入文字标题" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
            <el-select v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
              <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <!--      <el-form-item label="所属分类名称" prop="articleTypeName">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.articleTypeName"-->
          <!--          placeholder="请输入所属分类名称"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="作者" prop="author">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.author"-->
          <!--          placeholder="请输入作者"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="来源" prop="originFrom">-->
          <!--        <el-input-->
          <!--          v-model="queryParams.originFrom"-->
          <!--          placeholder="请输入来源"-->
          <!--          clearable-->
          <!--          size="small"-->
          <!--          @keyup.enter.native="handleQuery"-->
          <!--        />-->
          <!--      </el-form-item>-->
          <el-form-item label="发布日期">
            <el-date-picker v-model="daterangePublicDate" size="small" style="width: 240px" value-format="yyyy-MM-dd"
              type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['yq:article:add']">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['yq:article:edit']">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['yq:article:remove']">删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="文字标题" align="center" prop="title" />
          <el-table-column label="所属分类名称" align="center" prop="articleTypeName" />
          <el-table-column label="主图" align="center" prop="mainImgUrl" width="100">
            <template slot-scope="scope">
              <image-preview v-if="scope.row.mainImgUrl" :src="scope.row.mainImgUrl" :width="50" :height="50" />
              <img v-else src="@/assets/images/quexin.png" style="width: 80px;height: 80px">
            </template>
          </el-table-column>
          <el-table-column label="作者" align="center" prop="author" />
          <el-table-column label="来源" align="center" prop="originFrom" />
          <el-table-column width="200" label="语言类型" align="center" prop="languageType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType" />
            </template>
          </el-table-column>
          <el-table-column label="发布日期" align="center" prop="publicDate" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.publicDate, '{y}-{m}-{d}') }}</span>
            </template>
          </el-table-column>
          <el-table-column label="是否已禁用" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="orderNum" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['yq:article:edit']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCopy(scope.row)"
                v-hasPermi="['yq:article:edit']">复制
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['yq:article:remove']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改新闻对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="75%" append-to-body :close-on-click-modal="false">
          <!-- 添加多语言标签和翻译按钮 -->
          <div style="position: relative; margin-bottom: 20px;">
            <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
              @tab-click="handleLanguageTabClick">
              <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label" :name="lang.value">
              </el-tab-pane>
            </el-tabs>
            <div
              style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px; z-index: 1;">
              <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
                @click="handleAutoTranslate">自动翻译其他语言</el-button>
              <el-button size="mini" type="warning" icon="el-icon-delete"
                @click="handleClearOtherLanguages">清空其他语言</el-button>
            </div>
          </div>

          <el-form ref="form" :model="multiLanguageForms[activeLanguageTab] || form" :rules="rules" label-width="100px">
            <el-form-item label="文字标题" prop="title">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).title" placeholder="请输入文字标题" />
            </el-form-item>
            <!-- <el-form-item label="语言类型" prop="languageType">
              <el-select filterable v-model="(multiLanguageForms[activeLanguageTab] || form).languageType"
                placeholder="请选择语言类型" clearable size="small">
                <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
                  :value="dict.value" />
              </el-select>
            </el-form-item> -->
            <el-form-item label="所属分类" prop="articleTypeId">
              <treeselect v-model="(multiLanguageForms[activeLanguageTab] || form).articleTypeId" :options="typeOptions"
                :show-count="true" placeholder="请选择归属分类" />
            </el-form-item>

            <el-form-item label="简介" prop="description">
              <el-input type="textarea" :rows="4" show-word-limit maxlength="255"
                v-model="(multiLanguageForms[activeLanguageTab] || form).description" placeholder="请输入简介" />
            </el-form-item>
            <el-form-item label="作者" prop="author">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).author" placeholder="请输入作者" />
            </el-form-item>
            <el-form-item label="来源" prop="originFrom">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).originFrom" placeholder="请输入来源" />
            </el-form-item>
            <el-form-item label="发布日期" prop="publicDate">
              <el-date-picker clearable size="small"
                v-model="(multiLanguageForms[activeLanguageTab] || form).publicDate" type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择发布日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="规格" prop="specifications">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).specifications" placeholder="请输入规格" />
            </el-form-item>
            <el-form-item label="粒重" prop="grainWeight">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).grainWeight" placeholder="请输入粒重" />
            </el-form-item>
            <el-form-item label="外部链接" prop="grainWeight">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).externalLinks" placeholder="请输入外部链接" />
            </el-form-item>
            <el-form-item label="产品特点" prop="productFeatures">
              <el-input :rows="6" type="textarea" show-word-limit maxlength="200"
                v-model="(multiLanguageForms[activeLanguageTab] || form).productFeatures" placeholder="请输入产品特点" />
            </el-form-item>
            <el-form-item label="视频">
              <b-upload v-if="open" acceptType="video"
                :video-url.sync="(multiLanguageForms[activeLanguageTab] || form).videoUrl"></b-upload>
              <!--              <file-upload v-model="form.videoUrl"/>-->
            </el-form-item>
            <el-form-item label="主图" prop="mainImgUrl">
              <b-img-draggable v-if="open" :img-url.sync="(multiLanguageForms[activeLanguageTab] || form).mainImgUrl"
                :limit="1"></b-img-draggable>
            </el-form-item>
            <el-form-item label="轮播图" prop="bannerUrl">
              <b-img-draggable v-if="open" :img-url.sync="(multiLanguageForms[activeLanguageTab] || form).bannerUrl"
                :limit="5"></b-img-draggable>
            </el-form-item>
            <el-form-item label="是否已禁用">
              <el-radio-group v-model="(multiLanguageForms[activeLanguageTab] || form).status">
                <el-radio v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="详情内容" prop="content">
              <Tinymce v-if="open" ref="editor" v-model="(multiLanguageForms[activeLanguageTab] || form).content"
                :height="400" />
            </el-form-item>
            <el-form-item label="营养菜谱" prop="nutritionRecipe">
              <Tinymce v-if="open" ref="editor"
                v-model="(multiLanguageForms[activeLanguageTab] || form).nutritionRecipe" :height="400" />
            </el-form-item>
            <el-form-item label="排序" prop="orderNum">
              <el-input-number v-model="(multiLanguageForms[activeLanguageTab] || form).orderNum" :min="1" :max="999999"
                :precision="0" placeholder="请输入排序" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listArticle, getArticle, delArticle, addArticle, updateArticle, autoTranslateArticleData, batchSaveMultiLanguage, getByRelationGroup } from '@/api/yq/article'
import { treeselect } from '@/api/yq/articleType'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import HJUpload from '@/components/YqUpload/HJUpload'

export default {
  name: 'Product',
  dicts: ['status_yes_no', 'i18N_language'],
  components: { HJUpload, Treeselect },
  data() {
    return {
      activeName: 'first',
      showLanguageType: false,
      // 分类名称
      typeName: undefined,
      // 分类树选项
      typeOptions: undefined,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻表格数据
      articleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangePublicDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        typeCode: 2,
        articleTypeId: null,
        articleTypePath: null,
        articleTypeName: null,
        videoUrl: null,
        mainImgUrl: null,
        description: null,
        author: null,
        originFrom: null,
        publicDate: null,
        content: null,
        status: null,
        orderNum: null,
        orderByColumn: 'orderNum',
        languageType: null,
        externalLinks: null,
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: '文字标题不能为空', trigger: 'blur' }
        ],
        articleTypeId: [
          { required: true, message: '所属分类id不能为空', trigger: 'change' }
        ],
        // mainImgUrl: [
        //   { required: true, message: '主图url不能为空', trigger: 'blur' }
        // ],
        description: [
          { required: true, message: '简介不能为空', trigger: 'blur' }
        ],
        // author: [
        //   { required: true, message: '作者不能为空', trigger: 'blur' }
        // ],
        // originFrom: [
        //   { required: true, message: '来源不能为空', trigger: 'blur' }
        // ],
        publicDate: [
          { required: true, message: '发布日期不能为空', trigger: 'blur' }
        ],
        languageType: [
          { required: true, message: '语言类型 不能为空', trigger: 'change' }
        ]
      },
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前激活的语言标签页
      activeLanguageTab: 'zh_CN',
      // 是否正在翻译
      translating: false,
      // 是否已翻译过
      hasTranslated: false,
      // 是否正在提交
      submitting: false,
      // 当前关系组ID
      currentRelationGroupId: null
    }
  },
  computed: {
    // 支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  watch: {
    // 根据名称筛选部门树
    typeName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTreeselect()
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  mounted() {
    // 等待字典加载完成后初始化
    this.waitForDictAndInitialize()
  },
  methods: {
    // 等待字典加载完成后初始化
    async waitForDictAndInitialize() {
      // 等待字典加载完成
      if (!this.dict.type.i18N_language) {
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 100)
        return
      }

      this.initializeDefaultLanguage()
    },

    // 初始化默认语言
    initializeDefaultLanguage() {
      // 设置默认语言
      this.activeLanguageTab = 'zh_CN'

      // 初始化多语言表单
      this.initializeMultiLanguageForms()
    },

    // 初始化多语言表单
    initializeMultiLanguageForms() {
      if (!this.supportedLanguages || this.supportedLanguages.length === 0) {
        return
      }

      this.multiLanguageForms = {}

      // 为每个支持的语言创建表单
      this.supportedLanguages.forEach(lang => {
        this.$set(this.multiLanguageForms, lang.value, {
          id: null,
          title: null,
          typeCode: 2,
          articleTypeId: null,
          articleTypePath: null,
          articleTypeName: null,
          videoUrl: null,
          mainImgUrl: null,
          description: null,
          author: null,
          originFrom: null,
          publicDate: null,
          content: null,
          status: '0',
          orderNum: null,
          languageType: lang.value,
          nutritionRecipe: null,
          productFeatures: null,
          grainWeight: null,
          specifications: null,
          bannerUrl: null,
          relationGroupId: this.currentRelationGroupId,
          remark: null
        })
      })
    },

    // 加载多语言数据
    loadMultiLanguageData(relationGroupId) {
      if (!relationGroupId) {
        return
      }

      this.loading = true
      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []

        // 初始化多语言表单
        this.initializeMultiLanguageForms()

        // 填充多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.$set(this.multiLanguageForms, item.languageType, item)
          }
        })

        // 设置当前关系组ID
        this.currentRelationGroupId = relationGroupId

        this.loading = false
      }).catch(() => {
        this.loading = false
      })
    },

    handleClick(tab, event) {
      if (tab.index == 0) {
        if (this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        if (!this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    /** 查询分类下拉树结构 */
    getTreeselect() {
      treeselect({ typeCode: 2 }).then(response => {
        this.typeOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.articleTypeId = data.id
      this.handleQuery()
    },
    /** 查询新闻列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangePublicDate && '' != this.daterangePublicDate) {
        this.queryParams.params['beginPublicDate'] = this.daterangePublicDate[0]
        this.queryParams.params['endPublicDate'] = this.daterangePublicDate[1]
      }
      this.queryParams.typeCode = 2
      listArticle(this.queryParams).then(response => {
        this.articleList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        typeCode: 2,
        articleTypeId: null,
        articleTypePath: null,
        articleTypeName: null,
        videoUrl: null,
        mainImgUrl: null,
        description: null,
        author: null,
        originFrom: null,
        publicDate: null,
        content: undefined,
        status: '0',
        orderNum: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        languageType: null,
        nutritionRecipe: null,
        productFeatures: null,
        grainWeight: null,
        specifications: null,
        bannerUrl: null,
        remark: null
      }
      this.resetForm('form')
      this.multiLanguageForms = {} // 重置多语言表单数据
      this.activeLanguageTab = 'zh_CN' // 重置激活的语言标签页
      this.currentRelationGroupId = null // 重置关系组ID
      this.hasTranslated = false // 重置已翻译状态
      this.initializeMultiLanguageForms() // 初始化多语言表单
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.articleTypeId = null
      this.daterangePublicDate = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset()

      // 如果是从分类树点击"新增"，设置对应的分类ID
      if (this.queryParams.articleTypeId) {
        // 将当前查询的分类ID设置到当前语言的表单中
        if (this.multiLanguageForms[this.activeLanguageTab]) {
          this.multiLanguageForms[this.activeLanguageTab].articleTypeId = this.queryParams.articleTypeId;
        }
      }

      this.open = true
      this.title = '添加产品'
      // 初始化当前语言表单
      if (this.activeLanguageTab && this.multiLanguageForms[this.activeLanguageTab]) {
        this.multiLanguageForms[this.activeLanguageTab].languageType = this.activeLanguageTab
      }
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        this.form = response.data
        // 如果存在关系组ID，则加载多语言数据
        if (this.form.relationGroupId) {
          this.loadMultiLanguageData(this.form.relationGroupId)
        } else {
          // 初始化多语言表单
          this.initializeMultiLanguageForms()
          // 将当前数据填充到对应语言的表单
          if (this.form.languageType && this.multiLanguageForms[this.form.languageType]) {
            this.$set(this.multiLanguageForms, this.form.languageType, this.form)
          }
        }
        this.open = true
        this.title = '修改产品'
        this.activeLanguageTab = this.form.languageType || 'zh_CN' // 激活当前语言的标签页
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        this.form = response.data
        this.form.id = null

        // 如果存在关系组ID，则加载多语言数据
        if (this.form.relationGroupId) {
          this.loadMultiLanguageData(this.form.relationGroupId)
          // 清除所有表单的ID
          Object.keys(this.multiLanguageForms).forEach(lang => {
            if (this.multiLanguageForms[lang]) {
              this.multiLanguageForms[lang].id = null
            }
          })
          // 清除关系组ID，以便创建新的关系组
          this.currentRelationGroupId = null
          Object.keys(this.multiLanguageForms).forEach(lang => {
            if (this.multiLanguageForms[lang]) {
              this.multiLanguageForms[lang].relationGroupId = null
            }
          })
        } else {
          // 初始化多语言表单
          this.initializeMultiLanguageForms()
          // 将当前数据填充到对应语言的表单
          if (this.form.languageType && this.multiLanguageForms[this.form.languageType]) {
            this.$set(this.multiLanguageForms, this.form.languageType, this.form)
          }
        }

        this.open = true
        this.title = '复制产品'
        this.activeLanguageTab = this.form.languageType || 'zh_CN' // 激活当前语言的标签页
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.submitting = true

          // 检查是否有多语言数据
          const hasMultiLanguage = Object.keys(this.multiLanguageForms).length > 0

          if (hasMultiLanguage) {
            // 准备多语言数据
            const multiLanguageData = []
            Object.keys(this.multiLanguageForms).forEach(lang => {
              if (this.multiLanguageForms[lang] && this.multiLanguageForms[lang].title) {
                // 设置关系组ID
                this.multiLanguageForms[lang].relationGroupId = this.currentRelationGroupId
                multiLanguageData.push(this.multiLanguageForms[lang])
              }
            })

            // 批量保存多语言数据
            batchSaveMultiLanguage(multiLanguageData).then(response => {
              this.$modal.msgSuccess('保存成功')
              this.open = false
              this.getList()
            }).catch(error => {
              this.$modal.msgError('保存失败: ' + error)
            }).finally(() => {
              this.submitting = false
            })
          } else {
            // 单语言保存
            const formData = this.multiLanguageForms[this.activeLanguageTab] || this.form
            if (formData.id != null) {
              updateArticle(formData).then(response => {
                this.$modal.msgSuccess('修改成功')
                this.open = false
                this.getList()
              }).finally(() => {
                this.submitting = false
              })
            } else {
              addArticle(formData).then(response => {
                this.$modal.msgSuccess('新增成功')
                this.open = false
                this.getList()
              }).finally(() => {
                this.submitting = false
              })
            }
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('注意删除分类，将同步删除此数据关联的其他语言分类，是否确认删除产品编号为"' + ids + '"的数据项？').then(function () {
        return delArticle(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/article/export', {
        ...this.queryParams
      }, `article_${new Date().getTime()}.xlsx`)
    },
    /** 语言标签页点击事件 */
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
    },
    /** 自动翻译其他语言 */
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.title) {
        this.$modal.msgError('请先填写当前语言版本的标题')
        return
      }

      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm)
        }).catch(() => { })
      } else {
        this.executeTranslation(sourceForm)
      }
    },

    /** 执行翻译 */
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateArticleData(sourceForm).then(response => {
        const translatedData = response.data || {}

        // 更新多语言表单数据
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab && this.multiLanguageForms[lang]) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang].id
            const originalRelationGroupId = this.multiLanguageForms[lang].relationGroupId

            // 更新表单数据，保留原有分类ID或使用翻译返回的分类ID
            const articleTypeId = translatedData[lang].articleTypeId !== undefined ?
              translatedData[lang].articleTypeId :
              this.multiLanguageForms[lang].articleTypeId;

            // 更新表单数据
            this.$set(this.multiLanguageForms, lang, {
              ...translatedData[lang],
              id: originalId,
              relationGroupId: originalRelationGroupId || this.currentRelationGroupId,
              articleTypeId: articleTypeId, // 使用翻译API返回的分类ID或保留原有的
              typeCode: 2
            })
          }
        })

        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败: ' + error)
      }).finally(() => {
        this.translating = false
      })
    },

    /** 清空其他语言 */
    handleClearOtherLanguages() {
      this.$modal.confirm('确定要清空其他语言的数据吗？').then(() => {
        const currentLanguageCode = this.activeLanguageTab

        // 清空其他语言的数据
        this.supportedLanguages.forEach(lang => {
          if (lang.value !== currentLanguageCode) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].id : null
            const originalRelationGroupId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].relationGroupId : this.currentRelationGroupId

            // 创建新的表单数据
            this.$set(this.multiLanguageForms, lang.value, {
              id: originalId,
              title: null,
              typeCode: 2,
              articleTypeId: null, // 不复制分类ID，各语言独立
              articleTypePath: null,
              articleTypeName: null,
              videoUrl: null,
              mainImgUrl: null,
              description: null,
              author: null,
              originFrom: null,
              publicDate: null,
              content: null,
              status: '0',
              orderNum: null,
              languageType: lang.value,
              relationGroupId: originalRelationGroupId,
              nutritionRecipe: null,
              productFeatures: null,
              grainWeight: null,
              specifications: null,
              bannerUrl: null,
              remark: null
            })
          }
        })

        this.$modal.msgSuccess('清空成功')
      }).catch(() => { })
    }
  }
}
</script>

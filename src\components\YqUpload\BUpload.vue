<template>
  <div v-if="acceptType === 'image'">
    <div class="flex ">
      <div v-if="imgList.length>0 " v-for="(item,index) in imgList" :key="index" class="flex relative">
        <el-image
          :src="item" class="avatar"
          :style="`width: ${width}px; height: ${height}px;border: 1px dashed #d9d9d9;padding:${width/6}px;box-sizing:border-box;`"
          :preview-src-list="imgList">
        </el-image>
        <i
          class="el-icon-delete delete-uploader-icon"
          :style="`width: ${width/6}px; height: ${height/6}px; line-height: ${height/6}px;`"
          @click="deleteImg(index)"
        />
      </div>
      <el-upload
        multiple
        :action="baseUpload"
        :data="tokens"
        :limit="limit"
        :accept="getAccept"
        :before-upload="beforeUpload"
        :on-success="successUpload"
        :on-progress="onProgress"
        :show-file-list="false"
        :on-exceed="exceedHandle"
        :file-list="imgArr"
      >
        <i
          v-if="imgList.length<limit"
          class="el-icon-plus avatar-uploader-icon"
          :style="`width: ${width}px; height: ${height}px; line-height: ${height}px; border: 1px dashed #d9d9d9;`"
        />

      </el-upload>

    </div>


    <div slot="tip" class="el-upload__tip">只能上传{{ getAccept }}文件,最多{{limit}}项</div>
  </div>

  <div v-else-if="acceptType === 'video'">
    <div class="flex align-center ">
      <el-upload
        style="width: 360px; height: 180px;"
        class="avatar-uploader"
        drag
        :action="baseUpload"
        :data="tokens"
        :limit="1"
        :accept="getAccept"
        :before-upload="beforeUpload"
        :on-success="successUpload"
        :on-progress="onProgress"
        :on-change="handleChange"
        :show-file-list="false"
      >
        <video v-if="videoSrc && progressPercent == 0" id="videoFile" :src="videoSrc"
               style="width: 360px; height: 180px;"
               :name="videoName" controls>您的浏览器不支持 video 标签。
        </video>
        <i v-if="!videoSrc && progressPercent == 0" class="el-icon-upload"/>
        <div v-if="!videoSrc && progressPercent == 0" class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div v-if="!videoSrc && progressPercent > 0" class="el-upload__text" style="margin-top: 60px;">上传中，已上传{{
          progressPercent }}%
        </div>
      </el-upload>
      <i
        v-if="this.videoSrc"
        class="el-icon-delete "
        :style="`width: ${width/2}px; height: ${height/2}px; line-height: ${height/2}px;font-size:28px;margin-left:10px`"
        @click="deleteVideo"
      />
    </div>
    <div slot="tip" class="el-upload__tip">只能上传{{ getAccept }}文件</div>
  </div>
  <div v-else-if="acceptType === 'file'">

  </div>
</template>

<script>
  import { getUploadToken } from "../../api/upload/uploadApi";
  var COS = require('cos-js-sdk-v5');
  export default {
    name: 'BUpload',
    props: {
      cloudType: {
        type: String,
        default() {
          return process.env.VUE_APP_CLOUD_TYPE;
        }
      },
      acceptType: {
        type: String,
        default() {
          return 'image';
        }
      },
      fileName: {
        type: String,
        default() {
          return 'image.png';
        }
      },
      /* accept: {
         type: String,
         default() {
           return '.jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF';
         }
       },*/
      limit: {
        type: Number,
        default() {
          return 1;
        }
      },
      // 图片url，必须参数 || 视频url
      imgArr: {
        type: Array,
        default: () => {
          return []
        }
      },
      videoUrl: {
        type: String,
        default() {
          return ''
        }
      },
      width: {
        type: Number,
        default() {
          return 120;
        }
      },
      height: {
        type: Number,
        default() {
          return 120;
        }
      }
    },
    data() {
      return {
        progressPercent: 0,
        baseUpload: '',
        baseFileUrl: '',
        tokens: {},
        videoName: '',
        videoSrc: '',
        img: null,
        imgList: [],
        data: {}
      };
    },
    watch: {
      imgArr() {
        this.imgList = this.imgArr;
      },
      fileName() {
        this.videoName = this.fileName;
      },
      videoUrl() {
        this.videoSrc = this.videoUrl
      }
    },
    computed: {
      getAccept() {
        if (this.acceptType == 'image') {
          return '.jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF'
        } else if (this.acceptType == 'video') {
          return '.mp4'
        } else if (this.acceptType == 'audio') {
          return '.mp3,.wma'
        } else {
          return '.apk,wgt'
        }
      }
    },
    mounted() {
      this.imgList = this.imgArr || [];
      this.videoSrc = this.videoUrl || '';
      this.videoName = this.fileName;
    },
    methods: {
      /**
       * 文件超出个数限制时的钩子
       *
       * @param file
       */
      exceedHandle(files, fileList) {
        console.log(files, fileList)
        this.$message({
          message: `最多上传${this.limit}项`,
          type: 'warning'
        });
      },
      /**
       * 上传前的钩子
       * 主要通过第三方签名的形式，获取第三方签名token
       * @param file
       */
      async beforeUpload(file) {
        if (this.cloudType == 'oss') {
          return new Promise((resolve, reject) => {
            const fileForm = {value: file.lastModified + file.name};
            getUploadToken(fileForm).then(res => {
              console.log("-----mess",res)
              this.tokens.key = res.data.key;
              this.tokens.OSSAccessKeyId = res.data.params.OSSAccessKeyId;
              this.tokens.policy = res.data.params.policy;
              this.tokens.signature = res.data.params.signature;
              this.baseUpload = res.data.params.host;
              this.baseFileUrl = res.data.params.host;
              this.tokens.success_action_status = res.data.params.success_action_status;
              resolve(true);
            }, _ => {
              reject(false);
            });
          });
        }
        if (this.cloudType == 'cos') {
          console.log('cos')
          const fileForm = {value: file.lastModified + file.name};
          const that = this;
          await getUploadToken(fileForm).then(res => {
            this.data = res.data;
            var cos = new COS({
              SecretId: that.data.params.tokenJson.credentials.tmpSecretId,
              SecretKey: that.data.params.tokenJson.credentials.tmpSecretKey,
              XCosSecurityToken: that.data.params.tokenJson.credentials.sessionToken
            });
            const key = that.data.key;
            cos.putObject(
              {
                Bucket: that.data.params.bucket,
                Region: that.data.params.region,
                Key: key,
                Body: file
              },
              function (err, data) {
                if (data) {
                  console.log('1111', data)
                  if (that.acceptType == 'image') {

                    that.imgList.push(`http://${data.Location}`)
                    that.$emit('update:imgArr', that.imgList);
                  } else if (that.acceptType == 'video') {
                    that.videoSrc = `http://${data.Location}`
                    that.$emit('update:videoUrl', that.videoSrc);
                    that.progressPercent = 0;

                  }

                  // that.$emit('update:imgUrl', process.env.VUE_APP_BASE_API + key);
                  // that.$emit('update:fileKey', 'http://dow-1259795339.cos.ap-hongkong.myqcloud.com/' + key);
                }
                console.log(132, err || data);
              }
            );
          });
          // await this.$emit('update:imgUrl', process.env.VUE_APP_BASE_API + this.data.key);
        }
        if (this.cloudType == 'obs') {
          return new Promise((resolve, reject) => {
            const fileForm = {
              value: file.lastModified + file.name
            };
            getUploadToken(fileForm).then(res => {
              console.log(res);
              this.tokens.key = res.data.key;
              this.tokens.token = res.data.params.token
              this.baseUpload = res.data.params.host;
              this.baseFileUrl = res.data.params.host;
              resolve(true);
            }, _ => {
              reject(false);
            });
          });
        }

      },
      handleChange(file, fileList) {
        console.log('----handleChange---');
        console.log(this.videoName, file.name);
        console.log(file);
        if (file.percentage == 0) {
          this.img = null;
          this.$emit('update:imgUrl', this.img);
          this.videoName = file.name;
        }
      },
      /**
       * 上传成功的钩子
       * 根据具体业务操作
       * @param res
       * @param file
       * @param fileList
       */
      successUpload(res, file, fileList) {
        if (this.cloudType != 'cos') {
          console.log('----successUpload---');
          console.log('file---', file);
          console.log(this.progressPercent);
          this.progressPercent = 0;
          // this.img = `${this.baseFileUrl}/${this.tokens.key}`;
          if (this.acceptType == 'image') {
            this.imgList.push(`${this.baseFileUrl}/${this.tokens.key}`)
            this.$emit('update:imgArr', this.imgList);
            this.$emit('update:imgUrl', this.imgList.join(";"));
          }else{
            this.videoSrc=`${this.baseFileUrl}/${this.tokens.key}`
            this.$emit('update:videoUrl', this.videoSrc);

          }
        }

      },
      /**
       * 上传中 百分比
       * @param res
       * @param file
       * @param fileList
       */
      onProgress(event, file, fileList) {
        console.log('----onProgress1---');
        console.log(event);
        console.log(this.videoName, file.name);
        if (file.name == this.videoName) {
          this.progressPercent = event.percent.toFixed(2);
        }
      },
      deleteImg(index) {
        console.log('deleteImg')
        this.imgList.splice(index, 1)
        this.$emit('update:imgArr', this.imgList);
      },
      deleteVideo() {
        this.videoSrc = ''
        this.$emit('update:videoUrl', '');

      }
    }
  };
</script>

<style scoped>
  .avatar-uploader {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    text-align: center;
  }

  .delete-uploader-icon {
    /*background: #fff;*/
    font-size: 20px;
    color: #000;
    text-align: center;
    position: absolute;
    top: 0;
    right: 0;
  }

  .avatar {
    display: block;
  }
</style>

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入标题"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否启动" prop="enabled">
        <el-select filterable v-model="queryParams.enabled" placeholder="请选择轮播图位置">
          <el-option
            v-for="item in dict.type.status_yes_no"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生效日期" prop="time">
        <el-date-picker clearable size="small"
                        v-model="selectTime"
                        type="daterange"
                        value-format="yyyy-MM-dd"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['yq:banner:add']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['yq:banner:edit']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['yq:banner:remove']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="bannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="轮播图标题" align="center" prop="name"/>
      <el-table-column label="轮播图宣传图片" align="center" prop="imgUrl" width="120">
        <template slot-scope="scope">
          <image-preview :src="scope.row.imgUrl" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="center" prop="orderNum"/>
      <el-table-column label="图片停留时间(秒)" align="center" prop="staySecond"/>
      <el-table-column label="是否启动" align="center" prop="enabled">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.status_yes_no" :value="scope.row.enabled"/>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime">
        <template slot-scope="scope">
          {{ scope.row.startTime + ' 00:00:00' }}
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime">
        <template slot-scope="scope">
          {{ scope.row.endTime + ' 23:59:59' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['yq:banner:edit']"
          >修改
          </el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['yq:banner:remove']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改轮播图对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="160px">
        <el-form-item label="轮播图标题" prop="name">
          <el-input v-model="form.name" placeholder="请输入轮播图标题"/>
        </el-form-item>
        <el-form-item label="活动页面地址" prop="link">
          <el-input v-model="form.link" placeholder="请输入活动页面地址"/>
        </el-form-item>
        <el-form-item label="轮播图宣传图片" prop="imgUrl">
          <b-img-draggable :img-url.sync="form.imgUrl" :limit="1"></b-img-draggable>
        </el-form-item>
        <el-form-item label="轮播图位置" prop="position">
          <treeselect style="z-index: 99999999999999999" v-model="form.position" :options="cmsMenus"
                      :default-expand-level="1"
                      :normalizer="normalizer"
                      placeholder="请选择轮播图位置"
          >
            <label slot="option-label" slot-scope="{ node, shouldShowCount, count, labelClassName, countClassName }"
                   :class="labelClassName"
            >
              {{ node.label }} ： {{ node.raw.language }}
              <span v-if="shouldShowCount" :class="countClassName">({{ count }})</span>
            </label>
          </treeselect>
        </el-form-item>
        <el-form-item label="活动内容">
          <Tinymce ref="editor" v-model="form.content" :height="400"/>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="轮播图开始时间" prop="startTime">
              <el-date-picker clearable size="small"
                              v-model="form.startTime"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择轮播图开始时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="轮播图结束时间" prop="endTime">
              <el-date-picker clearable size="small"
                              v-model="form.endTime"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择轮播图结束时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="是否启动" prop="enabled">
          <el-radio-group v-model="form.enabled">
            <el-radio
              v-for="dict in dict.type.status_yes_no"
              :key="dict.value"
              :label="dict.value"
            >{{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="图片停留时间（秒）" prop="staySecond">
              <el-input-number v-model="form.staySecond" :min="1" :max="10" :precision="0"
                               placeholder="请输入图片停留时间（单位秒）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="orderNum">
              <el-input-number v-model="form.orderNum" :min="1" :max="99999" :precision="0" placeholder="请输入排序"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listBanner, getBanner, delBanner, addBanner, updateBanner } from '@/api/yq/banner'
import { cmsMenuList, cmsMenuTreeselect } from '@/api/yq/cmsMenu'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'

export default {
  name: 'Banner',
  components: {
    Treeselect
  },
  dicts: ['status_yes_no', 'i18N_language'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      selectTime: [],
      cmsMenus: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 轮播图表格数据
      bannerList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangeStartTime: [],
      // 备注时间范围
      daterangeEndTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        linkType: null,
        link: null,
        imgUrl: null,
        position: null,
        content: null,
        startTime: null,
        endTime: null,
        enabled: null,
        staySecond: null,
        orderNum: null,
        status: null,
        params: {
          beginStartTime: null,
          endStartTime: null
        }
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: '轮播图标题不能为空', trigger: 'blur' }
        ],
        linkType: [
          { required: true, message: '內链 in  外链 out不能为空', trigger: 'change' }
        ],
        link: [
          { required: true, message: '活动页面地址不能为空', trigger: 'blur' }
        ],
        imgUrl: [
          { required: true, message: '轮播图宣传图片不能为空', trigger: 'blur' }
        ],
        position: [
          { required: true, message: '轮播图位置：1则是首页不能为空', trigger: 'blur' }
        ],
        startTime: [
          { required: true, message: '轮播图开始时间不能为空', trigger: 'blur' }
        ],
        endTime: [
          { required: true, message: '轮播图结束时间不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    /** 转换新闻类型数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children
      }
      return {
        id: node.id,
        name: node.label,
        children: node.children
      }
    },
    /**
     * 获取cms菜单
     */
    getCmsMenus() {
      cmsMenuTreeselect().then(res => {
        this.cmsMenus = res.data
      })
    },

    /** 查询轮播图列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (this.selectTime.length > 0) {
        this.queryParams.params.beginStartTime = this.selectTime[0]
        this.queryParams.params.endStartTime = this.selectTime[1]
      }
      listBanner(this.queryParams).then(response => {
        this.bannerList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        linkType: null,
        link: null,
        imgUrl: null,
        position: null,
        content: null,
        startTime: null,
        endTime: null,
        enabled: '0',
        staySecond: null,
        orderNum: null,
        status: '0',
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        remark: null
      }
      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeStartTime = []
      this.daterangeEndTime = []
      this.selectTime = []
      this.resetForm('queryForm')

      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.getCmsMenus()
      this.open = true
      this.title = '添加轮播图'
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.getCmsMenus()
      const id = row.id || this.ids
      getBanner(id).then(response => {
        this.form = response.data
        if (this.form) {
          this.form.position = Number(this.form.position)
          this.form.enabled = String(this.form.enabled)
        }
        this.open = true
        this.title = '修改轮播图'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateBanner(this.form).then(response => {
              this.$modal.msgSuccess('修改成功')
              this.open = false
              this.getList()
            })
          } else {
            addBanner(this.form).then(response => {
              this.$modal.msgSuccess('新增成功')
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除轮播图编号为"' + ids + '"的数据项？').then(function() {
        return delBanner(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/banner/export', {
        ...this.queryParams
      }, `banner_${new Date().getTime()}.xlsx`)
    }
  }
}
</script>

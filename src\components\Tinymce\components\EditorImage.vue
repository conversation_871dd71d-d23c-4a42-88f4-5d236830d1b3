<template>
  <div class="upload-container">
    <el-button :style="{background:color,borderColor:color}" icon="el-icon-upload" size="mini" type="primary" @click=" btnClick">
      上传图片
    </el-button>
    <section style="display: none;">
      <el-upload
        :file-list="fileList"
        :show-file-list="true"
        list-type="picture-card"
        :on-remove="handleRemove"
        :on-success="handleSuccess"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        accept=".JPG, .PNG, .JPEG,.jpg, .png, .jpeg"
        :action="baseUpload"
        :data="tokens"
      >
        <el-button ref="toSelect" size="small" type="primary">
          选择文件
        </el-button>
      </el-upload>
      <el-button @click="dialogVisible = false">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit">
        确认
      </el-button>
    </section>
  </div>
</template>

<script>
// import { getToken } from 'api/qiniu'
// import { baseUpload, baseFileUrl } from '../../../contants/contants'

import {getUploadToken} from "../../../api/upload/uploadApi";

export default {
  name: 'EditorSlideUpload',
  props: {
    color: {
      type: String,
      default: '#1890ff'
    },
    cloudType: {
      type: String,
      default() {
        return process.env.VUE_APP_CLOUD_TYPE;
      }
    },
  },
  data() {
    return {
      baseUpload: '',
      baseFileUrl: '',
      tokens: {},
      dialogVisible: false,
      listObj: {},
      fileList: []
    };
  },
  methods: {
    btnClick() {
      this.$refs.toSelect.$el.click();
    },
    checkAllSuccess() {
      return Object.keys(this.listObj).every(item => this.listObj[item].hasSuccess);
    },
    handleSubmit() {
      console.log("========handleSubmit==");
      const arr = Object.keys(this.listObj).map(v => this.listObj[v]);
      if (!this.checkAllSuccess()) {
        this.$message('Please wait for all images to be uploaded successfully. If there is a network problem, please refresh the page and upload again!');
        return;
      }
      this.$emit('successCBK', arr);
      this.listObj = {};
      this.fileList = [];
      this.dialogVisible = false;
    },
    handleSuccess(response, file) {
      console.log("========handleSuccess==");
      const uid = file.uid;
      const objKeyArr = Object.keys(this.listObj);
      console.log(2111);
      const url = this.baseFileUrl + '/' + this.tokens.key;
      console.log(url);
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          this.listObj[objKeyArr[i]].url = url;
          this.listObj[objKeyArr[i]].hasSuccess = true;
          break;
        }
      }
      this.handleSubmit();
    },
    handleRemove(file) {
      const uid = file.uid;
      const objKeyArr = Object.keys(this.listObj);
      for (let i = 0, len = objKeyArr.length; i < len; i++) {
        if (this.listObj[objKeyArr[i]].uid === uid) {
          delete this.listObj[objKeyArr[i]];
          return;
        }
      }
    },
    async beforeUpload(file) {
      const fileName = file.uid;
      this.listObj[fileName] = {};
      const fileForm = {
        value: file.lastModified + file.name
      };
      let result ;
      if (this.cloudType == 'oss') {
        result = await this.getOssInfo(fileForm,file);
      }
      if (this.cloudType == 'obs') {
        result =await this.getObsInfo(fileForm,file);
      }
      return result;
    },
    getOssInfo(fileForm,file){
      const _self = this;
      return new Promise((resolve, reject) => {
        getUploadToken( fileForm)
          .then((res) => {
            console.log(res, '-------upload/token------');
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tokens.key = res.data.key;
            this.tokens.OSSAccessKeyId = res.data.params.OSSAccessKeyId;
            this.tokens.policy = res.data.params.policy;
            this.tokens.signature = res.data.params.signature;
            this.tokens.success_action_status = res.data.params.success_action_status;
            _self.listObj[file.uid] = { hasSuccess: false, uid: file.uid };
            resolve(true);
          }, _ => {
            reject(false);
          });
      });
    },
    getObsInfo(fileForm,file){
      const _self = this;
      return new Promise((resolve, reject) => {
        getUploadToken(fileForm).then(res => {
          console.log(res);
          this.tokens.key = res.data.key;
          this.tokens.token = res.data.params.token
          this.baseUpload = res.data.params.host;
          this.baseFileUrl = res.data.params.host;
          _self.listObj[file.uid] = { hasSuccess: false, uid: file.uid };
          resolve(true);
        }, _ => {
          reject(false);
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.editor-slide-upload {
  margin-bottom: 20px;
  ::v-deep .el-upload--picture-card {
    width: 100%;
  }
}
</style>

<template>
  <div class="upload-file">
    <el-upload
      v-show="fileList && fileList.length < limit && !readonly"
      ref="upload"
      class="upload-file-box"
      multiple
      drag
      :action="baseUpload"
      :data="tokens"
      :limit="limit"
      :accept="getAccept"
      :before-upload="beforeUpload"
      :on-success="successUpload"
      :on-progress="onProgress"
      :show-file-list="false"
      :on-exceed="exceedHandle"
      :file-list="fileList"
    >
      <div class="el-upload__box">
        <div class="el-upload__text"><em><i class="el-icon-upload2"></i>{{fileType}}</em>上传</div>
        <div slot="tip">上传最多{{limit}}项</div>
      </div>
    </el-upload>
    <div v-if="showFile">
      <div v-if="acceptType === 'image'">
        <div
          v-for="(item,index) in fileList"
          class="image-item"
          :style="{ backgroundImage: `url(${item.fileUrl})` }"
          @mouseover.prevent="item.is_hover = true;$forceUpdate()"
          @mouseleave.prevent="item.is_hover = false;$forceUpdate()"
        >
          <div v-show="!item.is_hover" class="label">
            <i class="el-icon-upload-success el-icon-check icon-success"/>
          </div>
          <div v-show="item.is_hover && !readonly" class="mask">
            <i class="el-icon-zoom-in bin" style="left: 30%" @click="dialogImageUrl = null;dialogVisible = true;dialogImageUrl = item.fileUrl"/>
            <i class="el-icon-delete bin" style="left: 60%;" @click="removeFile(index)"/>
          </div>
          <div v-show="item.is_hover && readonly" class="mask">
            <i class="el-icon-zoom-in bin" @click="dialogImageUrl = null;dialogVisible = true;dialogImageUrl = item.fileUrl"/>
          </div>
        </div>
      </div>
      <div v-else>
        <!--   图片展示   -->
        <div class="imageBox">
          <div
            v-for="(item,index) in fileList"
            v-if="/\.(png|jpg|gif|jpeg|webp)$/.test(item.fileUrl)"
            :key="index"
            class="image-item"
            :style="{ backgroundImage: `url(${item.fileUrl})` }"
            @mouseover.prevent="item.is_hover = true;$forceUpdate()"
            @mouseleave.prevent="item.is_hover = false;$forceUpdate()"
          >
            <div v-show="!item.is_hover" class="label">
              <i class="el-icon-upload-success el-icon-check icon-success"/>
            </div>
            <div v-show="item.is_hover && !readonly" class="mask">
              <i class="el-icon-zoom-in bin" style="left: 30%" @click="dialogImageUrl = null;dialogVisible = true;dialogImageUrl = item.fileUrl"/>
              <i class="el-icon-delete bin" style="left: 60%;" @click="removeFile(index)"/>
            </div>
            <div v-show="item.is_hover && readonly" class="mask">
              <i class="el-icon-zoom-in bin" @click="dialogImageUrl = null;dialogVisible = true;dialogImageUrl = item.fileUrl"/>
            </div>
          </div>
        </div>

        <!--   文件展示   -->
        <div
          class="fileBox"
          v-for="(item,index) in fileList"
          v-if="!/\.(png|jpg|gif|jpeg|webp)$/.test(item.fileUrl)"
          @click="toPrivew(item.fileUrl)"
          :key="index"
        >
          <el-tooltip effect="dark" :content="item.fileName" placement="top">
            <div class="fileBox__text">
              {{item.fileName}}
            </div>
          </el-tooltip>
          <div class="fileBox__close" v-if="!readonly" @click.stop="removeFile(index)">
            <i class="el-icon-delete"></i>
          </div>
        </div>
      </div>
    </div>

    <!--  图片预览  -->
    <el-dialog
      :visible.sync="dialogVisible"
      title="预览"
      width="800"
      append-to-body
    >
      <img
        :src="dialogImageUrl"
        style="display: block; max-width: 100%; margin: 0 auto"
      />
    </el-dialog>
  </div>
</template>


<script>
import { getUploadToken } from '@/api/upload/uploadApi';
const COS = require('cos-js-sdk-v5');
export default {
  name: 'HJUpload',
  props: {
    cloudType: {
      type: String,
      default() {
        return process.env.VUE_APP_CLOUD_TYPE;
      }
    },
    limit: {
      type: Number,
      default() {
        return 1;
      }
    },
    fileArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    acceptType: {
      type: String
    },
    readonly:{
      type: Boolean,
      default(){
        return false;
      }
    },
    showFile:{
      type: Boolean,
      default(){
        return true;
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: null,
      progressPercent: 0,
      baseUpload: '',
      baseFileUrl: '',
      tokens: {},
      videoName: '',
      videoSrc: '',
      img: null,
      fileList: [],
      data: {},
      // 临时文件数组
      tempList:[],
      fileType: '',
      loadingInstance: null
    };
  },
  watch: {
    fileArr() {
      this.fileList = this.fileArr && this.fileArr.map(item=>Object.assign(item,{is_hover:false})) || [];
    }
  },
  computed: {
    getAccept() {
      if (this.acceptType == 'image') {
        this.fileType = '图片'
        return '.jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PBG,.GIF'
      } else if (this.acceptType == 'video') {
        this.fileType = '视频'
        return '.mp4'
      } else if (this.acceptType == 'audio') {
        this.fileType = '音频'
        return '.mp3,.wma'
      } else if (this.acceptType == 'apk') {
        this.fileType = '应用安装包'
        return '.apk'
      }else if (this.acceptType == 'pdf') {
        this.fileType = 'pdf文件'
        return '.pdf,.PDF'
      }else {
        this.fileType = '文件'
        return;
      }
    }
  },
  mounted() {
    this.fileList = this.fileArr && this.fileArr.map(item=>Object.assign(item,{is_hover:false})) || [];
    this.videoSrc = this.videoUrl || '';
    this.videoName = this.fileName;
    console.log(this.fileList)
  },
  methods: {
    /**
     * 文件超出个数限制时的钩子
     *
     * @param file
     */
    exceedHandle(files, fileList) {
      console.log(files, fileList)
      this.$message({
        message: `最多上传${this.limit}项`,
        type: 'warning'
      });
    },
    /**
     * 上传前的钩子
     * 主要通过第三方签名的形式，获取第三方签名token
     * @param file
     */
    async beforeUpload(file) {
		let fileName = file.name.substring(file.name.lastIndexOf('.') + 1)
		if(this.acceptType && fileName != this.acceptType) {
			this.$modal.msgError(`请上传格式为${this.acceptType}的文件`)
			return false
		}
      this.loadingInstance = this.$loading({
        lock: true,
        text: `文件上传中。。。`,
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.cloudType == 'oss') {
        return new Promise((resolve, reject) => {
          const fileForm = {value: file.lastModified + file.name};
          getUploadToken(fileForm).then(res => {
            console.log("-----mess",res)
            this.tokens.key = res.data.key;
            this.tokens.OSSAccessKeyId = res.data.params.OSSAccessKeyId;
            this.tokens.policy = res.data.params.policy;
            this.tokens.signature = res.data.params.signature;
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tokens.success_action_status = res.data.params.success_action_status;
            resolve(true);
          }, _ => {
            reject(false);
          });
        });
      }
      if (this.cloudType == 'cos') {
        console.log('cos')
        const fileForm = {value: file.lastModified + file.name};
        const that = this;
        await getUploadToken(fileForm).then(res => {
          this.data = res.data;
          var cos = new COS({
            SecretId: that.data.params.tokenJson.credentials.tmpSecretId,
            SecretKey: that.data.params.tokenJson.credentials.tmpSecretKey,
            XCosSecurityToken: that.data.params.tokenJson.credentials.sessionToken
          });
          const key = that.data.key;
          cos.putObject(
            {
              Bucket: that.data.params.bucket,
              Region: that.data.params.region,
              Key: key,
              Body: file
            },
            function (err, data) {
              if (data) {
                console.log('1111', data)
                if (that.acceptType == 'image') {

                  that.fileList.push(`http://${data.Location}`)
                  that.$emit('update:fileArr', that.fileList);
                } else if (that.acceptType == 'video') {
                  that.videoSrc = `http://${data.Location}`
                  that.$emit('update:videoUrl', that.videoSrc);
                  that.progressPercent = 0;

                }

                // that.$emit('update:imgUrl', process.env.VUE_APP_BASE_API + key);
                // that.$emit('update:fileKey', 'http://dow-1259795339.cos.ap-hongkong.myqcloud.com/' + key);
              }
              console.log(132, err || data);
            }
          );
        });
        // await this.$emit('update:imgUrl', process.env.VUE_APP_BASE_API + this.data.key);
      }
      if (this.cloudType == 'obs') {
        return new Promise((resolve, reject) => {
          const fileForm = {
            value: file.lastModified + file.name
          };
          getUploadToken(fileForm).then(res => {
            console.log(res);
            this.tokens.key = res.data.key;
            this.tokens.token = res.data.params.token
            this.baseUpload = res.data.params.host;
            this.baseFileUrl = res.data.params.host;
            this.tempList.push({
              name: file.name,
              key: res.data.key,
              token: res.data.params.token
            });
            resolve(true);
          }, _ => {
            reject(false);
          });
        });
      }
    },
    /**
     * 上传成功的钩子
     * 根据具体业务操作
     * @param res
     * @param file
     * @param fileList
     */
    successUpload(res, file, fileList) {
      if (this.cloudType != 'cos') {
        if(this.$refs.upload.uploadFiles.filter(item=>item.status === 'success').length === this.$refs.upload.uploadFiles.length){
          console.log('----successUpload---');
          console.log('file---', file);
          console.log(this.progressPercent);
          let obj = {}
          this.progressPercent = 0;
          if (this.tempList.length > 0) {
            this.tempList.map(item=>{
              obj = {
                fileName: item.name,
                fileUrl: `${this.baseFileUrl}/${item.key}`,
                is_hover: false
              };
              this.fileList.push(obj);
            })
            this.tempList = []
          }
          this.$emit('update:fileArr', this.fileList);
          this.$emit('file-change', this.fileList);
          this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
            this.loadingInstance.close();
          });
        }
      }
    },
    toPrivew(url){
      window.open(url)
    },
    removeFile(index){
      this.fileList.splice(index, 1)
      this.$emit('update:fileArr', this.fileList);
      this.$refs.upload.uploadFiles.splice(index,1)//上传列表对应删除，否则limit错乱
    },
    /**
     * 上传中 百分比
     * @param res
     * @param file
     * @param fileList
     */
    onProgress(event, file, fileList) {
      console.log('----onProgress1---');
      console.log(fileList);
    },
    deleteImg(index) {
      console.log('deleteImg')
      this.fileList.splice(index, 1)
      this.$emit('update:fileArr', this.fileList);
    },
    deleteVideo() {
      this.videoSrc = ''
      this.$emit('update:videoUrl', '');
    }
  }
};
</script>

<style lang="scss" scoped>
.upload-file{
  .upload-file-box{
    width: 100%;
    height: 100%;
    .el-upload__box{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;
      width: 100%;
    }
    ::v-deep .el-upload,
    ::v-deep .el-upload-dragger{
      width: 100%;
      height: 100%;
    }
  }
}
.image-item{
  width: 148px;
  height: 148px;
  position: relative;
  margin-right: 10px;
  margin-bottom: 10px;
  border: 1px solid #c0ccda;
  background-size: 100% 100%;
  border-radius: 6px;
  float: left;
  overflow: hidden;
  cursor: pointer;
  .label{
    width: 46px;
    height: 26px;
    background-color: #13ce66;
    color: #FFFFFF;
    transform: rotate(45deg);
    text-align: center;
    position: absolute;
    right: -17px;
    top: -7px;
    .icon-success{
      transform: rotate(-45deg)
    }
  }
  .mask{
    width: 100%;
    height: 100%;
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.5);
    position: relative;
    .bin{
      color: #FFFFFF;
      font-size: 20px;
      position: absolute;
      left: 45%;
      top: 43%;
    }
  }
}
.imageBox{
  display: flex;
  flex-wrap: wrap;
}

.fileBox{
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all .3s;
  border-radius: 5px;
  &:hover{
    color: #1890ff;
    background-color: #f8f8f8;
    cursor: pointer;
    .fileBox__close{
      opacity: 1;
    }
  }
  .fileBox__text{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .fileBox__close{
    border-radius: 50%;
    padding:0 0 0 15px;
    transition: all .3s;
    color: black;
    opacity: 0;
    &:hover{
      color: red;
    }
  }

}
</style>

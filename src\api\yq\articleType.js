import request from '@/utils/request'

// 查询新闻类型列表
export function listArticleType(query) {
  return request({
    url: '/yq/articleType/list',
    method: 'get',
    params: query
  })
}

// 查询下拉树结构
export function treeselect(query) {
  return request({
    url: '/yq/articleType/treeselect',
    method: 'get',
    params: query
  })
}
// 查询新闻类型详细
export function getArticleType(id) {
  return request({
    url: '/yq/articleType/' + id,
    method: 'get'
  })
}

// 新增新闻类型
export function addArticleType(data) {
  return request({
    url: '/yq/articleType',
    method: 'post',
    data: data
  })
}

// 修改新闻类型
export function updateArticleType(data) {
  return request({
    url: '/yq/articleType',
    method: 'put',
    data: data
  })
}

// 删除新闻类型
export function delArticleType(id) {
  return request({
    url: '/yq/articleType/' + id,
    method: 'delete'
  })
}

// 自动翻译文章类型（通过ID）
export function autoTranslateArticleType(id) {
  return request({
    url: '/yq/articleType/autoTranslate/' + id,
    method: 'post'
  })
}

// 自动翻译新闻类型数据（用于前端传递的原始数据）
export function autoTranslateArticleTypeData(data) {
  return request({
    url: '/yq/articleType/autoTranslateData',
    method: 'post',
    data: data
  })
}

// 批量保存多语言版本的新闻类型
export function batchSaveMultiLanguage(data) {
  return request({
    url: '/yq/articleType/batchSave',
    method: 'post',
    data: data
  })
}

// 根据关系组ID获取所有语言版本
export function getByRelationGroup(relationGroupId) {
  return request({
    url: `/yq/articleType/getByRelationGroup/${relationGroupId}`,
    method: 'get'
  })
}

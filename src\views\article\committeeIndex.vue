<template>
  <div class="app-container">

    <el-row :gutter="20">
      <!--分类数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input v-model="typeName" placeholder="请输入分类名称" clearable size="small" prefix-icon="el-icon-search"
            style="margin-bottom: 20px" />
        </div>
        <div class="head-container">
          <el-tree :data="typeOptions" :props="defaultProps" :expand-on-click-node="false"
            :filter-node-method="filterNode" :highlight-current="true" ref="tree" default-expand-all
            @node-click="handleNodeClick" />
        </div>
      </el-col>
      <!--文章数据-->
      <el-col :span="20" :xs="24">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="中文文章" name="first"></el-tab-pane>
          <el-tab-pane label="其他语言文章" name="second"></el-tab-pane>
        </el-tabs>

        <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="名称" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入名称" clearable size="small"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item v-if="showLanguageType" label="语言类型" prop="languageType">
            <el-select v-model="queryParams.languageType" placeholder="请选择语言类型" clearable size="small">
              <el-option v-for="dict in dict.type.i18N_language" :key="dict.value" :label="dict.label"
                :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
              v-hasPermi="['yq:article:add']">新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
              v-hasPermi="['yq:article:edit']">修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
              v-hasPermi="['yq:article:remove']">删除
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="articleList" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="名称" align="center" prop="title" />
          <el-table-column label="所属分类名称" align="center" prop="articleTypeName" />
          <!--          <el-table-column label="主图" align="center" prop="mainImgUrl" width="100">-->
          <!--            <template slot-scope="scope">-->
          <!--              <image-preview v-if="scope.row.mainImgUrl" :src="scope.row.mainImgUrl" :width="50" :height="50"/>-->
          <!--              <img v-else src="@/assets/images/quexin.png" style="width: 80px;height: 80px">-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <!--          <el-table-column label="作者" align="center" prop="author"/>-->
          <!--          <el-table-column label="来源" align="center" prop="originFrom"/>-->
          <el-table-column width="200" label="语言类型" align="center" prop="languageType">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.i18N_language" :value="scope.row.languageType" />
            </template>
          </el-table-column>
          <!--          <el-table-column label="发布日期" align="center" prop="publicDate" width="180">-->
          <!--            <template slot-scope="scope">-->
          <!--              <span>{{ parseTime(scope.row.publicDate, '{y}-{m}-{d}') }}</span>-->
          <!--            </template>-->
          <!--          </el-table-column>-->
          <el-table-column label="是否主任" align="center" prop="status">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.status_yes_no" :value="scope.row.status" />
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="orderNum" />
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                v-hasPermi="['yq:article:edit']">修改
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleCopy(scope.row)"
                v-hasPermi="['yq:article:edit']">复制
              </el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['yq:article:remove']">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getList" />

        <!-- 添加或修改新闻对话框 -->
        <el-dialog :title="title" :visible.sync="open" width="75%" append-to-body :close-on-click-modal="false">
          <!-- 语言标签页和按钮 -->
          <div style="position: relative; margin-bottom: 20px;">
            <el-tabs v-if="supportedLanguages && supportedLanguages.length > 0" v-model="activeLanguageTab"
              @tab-click="handleLanguageTabClick">
              <el-tab-pane v-for="lang in supportedLanguages" :key="lang.value" :label="lang.label"
                :name="lang.value"></el-tab-pane>
            </el-tabs>

            <div
              style="position: absolute; top: 0; right: 0; margin-top: 3px; background-color: #fff; padding-left: 15px; z-index: 1;">
              <el-button size="mini" type="success" icon="el-icon-refresh" :loading="translating"
                @click="handleAutoTranslate">自动翻译其他语言</el-button>
              <el-button size="mini" type="warning" icon="el-icon-delete"
                @click="handleClearOtherLanguages">清空其他语言</el-button>
            </div>
          </div>

          <el-form ref="form" :model="multiLanguageForms[activeLanguageTab] || form" :rules="rules" label-width="100px">
            <el-form-item label="名称" prop="title">
              <el-input v-model="(multiLanguageForms[activeLanguageTab] || form).title" placeholder="请输入名称" />
            </el-form-item>
            <el-form-item label="所属分类" prop="articleTypeId">
              <treeselect v-model="(multiLanguageForms[activeLanguageTab] || form).articleTypeId" :options="typeOptions"
                :show-count="true" placeholder="请选择归属分类" />
            </el-form-item>
            <el-form-item label="是否主任">
              <el-radio-group v-model="(multiLanguageForms[activeLanguageTab] || form).status">
                <el-radio v-for="dict in dict.type.status_yes_no" :key="dict.value" :label="dict.value">{{ dict.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="排序" prop="orderNum">
              <el-input-number v-model="(multiLanguageForms[activeLanguageTab] || form).orderNum" :min="1" :max="999999"
                :precision="0" placeholder="请输入排序" />
            </el-form-item>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { listArticle, getArticle, delArticle, addArticle, updateArticle, autoTranslateArticleData, batchSaveMultiLanguage, getByRelationGroup } from '@/api/yq/article'
import { treeselect } from '@/api/yq/articleType'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
import HJUpload from '@/components/YqUpload/HJUpload'

export default {
  name: 'Product',
  dicts: ['status_yes_no', 'i18N_language'],
  components: { HJUpload, Treeselect },
  data() {
    return {
      activeName: 'first',
      showLanguageType: false,
      // 分类名称
      typeName: undefined,
      // 分类树选项
      typeOptions: undefined,
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 新闻表格数据
      articleList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangePublicDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: null,
        typeCode: 3,
        articleTypeId: null,
        articleTypePath: null,
        articleTypeName: null,
        videoUrl: null,
        mainImgUrl: null,
        description: null,
        author: null,
        originFrom: null,
        publicDate: null,
        content: null,
        status: null,
        orderNum: null,
        orderByColumn: 'orderNum',
        languageType: null,
        externalLinks: null,
        isAsc: 'desc'
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: '名称不能为空', trigger: 'blur' }
        ],
        articleTypeId: [
          { required: true, message: '所属分类id不能为空', trigger: 'change' }
        ],
        languageType: [
          { required: true, message: '语言类型 不能为空', trigger: 'change' }
        ]
      },
      // 多语言表单数据
      multiLanguageForms: {},
      // 当前激活的语言标签页
      activeLanguageTab: 'zh_CN',
      // 是否正在翻译
      translating: false,
      // 是否已翻译过(用于判断是否需要二次确认)
      hasTranslated: false,
      // 是否正在提交
      submitting: false,
      // 当前编辑的关系组ID
      currentRelationGroupId: null
    }
  },
  computed: {
    // 从字典中获取支持的语言列表
    supportedLanguages() {
      return this.dict.type.i18N_language || []
    }
  },
  watch: {
    // 根据名称筛选部门树
    typeName(val) {
      this.$refs.tree.filter(val)
    }
  },
  created() {
    this.getTreeselect()
    this.queryParams.languageType = 'zh_CN'
    this.getList()
  },
  mounted() {
    // 等待字典数据加载完成后再初始化
    this.$nextTick(() => {
      this.waitForDictAndInitialize()
    })
  },
  methods: {
    // 等待字典数据加载并初始化
    waitForDictAndInitialize() {
      // 检查字典数据是否已加载
      if (this.dict && this.dict.type && this.dict.type.i18N_language && this.dict.type.status_yes_no) {
        this.initializeDefaultLanguage()
      } else {
        // 如果字典数据还未加载，延迟重试
        setTimeout(() => {
          this.waitForDictAndInitialize()
        }, 50)
      }
    },
    // 初始化默认语言
    initializeDefaultLanguage() {
      const languages = this.dict.type.i18N_language
      // 优先选择中文，如果没有则选择第一个
      const zhCN = languages.find(lang => lang.value === 'zh_CN')
      this.activeLanguageTab = zhCN ? 'zh_CN' : languages[0].value
      this.initializeMultiLanguageForms()
    },
    // 初始化多语言表单
    initializeMultiLanguageForms() {
      const languages = this.dict.type.i18N_language
      languages.forEach(lang => {
        if (!this.multiLanguageForms[lang.value]) {
          this.$set(this.multiLanguageForms, lang.value, {
            id: null,
            title: null,
            articleTypeId: null,
            articleTypePath: null,
            typeCode: 3,
            articleTypeName: null,
            status: '0',
            orderNum: null,
            createBy: null,
            createTime: null,
            updateTime: null,
            updateBy: null,
            delFlag: null,
            languageType: lang.value,
            remark: null
          })
        }
      })
    },
    // 加载多语言数据
    loadMultiLanguageData(relationGroupId) {
      if (!relationGroupId) {
        return
      }

      getByRelationGroup(relationGroupId).then(response => {
        const multiLanguageData = response.data || []
        // 清空现有数据
        this.multiLanguageForms = {}
        this.initializeMultiLanguageForms()

        // 填充已有的多语言数据
        multiLanguageData.forEach(item => {
          if (this.multiLanguageForms[item.languageType]) {
            this.$set(this.multiLanguageForms, item.languageType, { ...item })
          }
        })

        // 设置默认激活的tab为中文
        this.activeLanguageTab = 'zh_CN'
      }).catch(error => {
        this.$modal.msgError('加载多语言数据失败：' + (error.msg || error.message))
      })
    },

    handleClick(tab, event) {
      if (tab.index == 0) {
        if (this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = false
        this.queryParams.languageType = 'zh_CN'
      } else {
        if (!this.showLanguageType) {
          this.queryParams.title = ''
        }
        this.showLanguageType = true
        this.queryParams.languageType = 'en_US'
      }
      this.getList()
    },
    /** 查询分类下拉树结构 */
    getTreeselect() {
      treeselect({ typeCode: 3 }).then(response => {
        this.typeOptions = response.data
      })
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.articleTypeId = data.id
      this.handleQuery()
    },
    /** 查询新闻列表 */
    getList() {
      this.loading = true
      this.queryParams.params = {}
      if (null != this.daterangePublicDate && '' != this.daterangePublicDate) {
        this.queryParams.params['beginPublicDate'] = this.daterangePublicDate[0]
        this.queryParams.params['endPublicDate'] = this.daterangePublicDate[1]
      }
      this.queryParams.typeCode = 3
      listArticle(this.queryParams).then(response => {
        this.articleList = response.data.rows
        this.total = response.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        title: null,
        typeCode: 3,
        articleTypeId: null,
        articleTypePath: null,
        articleTypeName: null,
        status: '0',
        orderNum: null,
        createBy: null,
        createTime: null,
        updateTime: null,
        updateBy: null,
        delFlag: null,
        languageType: null,
        remark: null
      }
      // 重置多语言表单数据
      this.multiLanguageForms = {}
      this.currentRelationGroupId = null
      // 重置翻译状态
      this.hasTranslated = false
      // 确保字典数据可用后再初始化
      if (this.dict && this.dict.type && this.dict.type.i18N_language) {
        this.initializeMultiLanguageForms()
        this.initializeDefaultLanguage()
      }

      this.resetForm('form')
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.articleTypeId = null
      this.daterangePublicDate = []
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = '添加委员会成员'

      // 设置默认语言类型
      this.multiLanguageForms[this.activeLanguageTab].languageType = this.activeLanguageTab
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        const currentData = response.data

        // 检查是否有多语言关联关系
        if (currentData.relationGroupId) {
          this.currentRelationGroupId = currentData.relationGroupId
          // 获取所有语言版本
          this.loadMultiLanguageData(currentData.relationGroupId)
        } else {
          // 没有关联关系，只加载当前语言版本
          this.multiLanguageForms[currentData.languageType] = { ...currentData }
          this.activeLanguageTab = currentData.languageType
        }

        this.open = true
        this.title = '修改委员会成员'
      })
    },
    /** 复制按钮操作 */
    handleCopy(row) {
      this.reset()
      const id = row.id || this.ids
      getArticle(id).then(response => {
        const sourceData = response.data
        // 检查是否有多语言关联关系
        if (sourceData.relationGroupId) {
          // 加载多语言数据
          getByRelationGroup(sourceData.relationGroupId).then(relationResponse => {
            const multiLanguageData = relationResponse.data || []

            // 为每个语言创建副本并清除ID和关系组ID
            multiLanguageData.forEach(item => {
              if (this.multiLanguageForms[item.languageType]) {
                this.$set(this.multiLanguageForms, item.languageType, {
                  ...item,
                  id: null,
                  relationGroupId: null
                })
              }
            })

            // 设置当前活跃的标签页
            this.activeLanguageTab = sourceData.languageType || 'zh_CN'
          })
        } else {
          // 只复制当前语言版本
          this.multiLanguageForms[sourceData.languageType] = {
            ...sourceData,
            id: null
          }
          this.activeLanguageTab = sourceData.languageType
        }

        this.open = true
        this.title = '复制委员会成员'
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 准备要提交的数据
          const dataToSave = {};

          // 添加有数据的表单
          this.supportedLanguages.forEach(lang => {
            const formData = this.multiLanguageForms[lang.value];
            if (formData && (formData.title || formData.id)) {
              dataToSave[lang.value] = { ...formData };
            }
          });

          if (Object.keys(dataToSave).length === 0) {
            this.$modal.msgError('请至少填写一个语言版本的数据');
            return;
          }

          this.submitting = true;
          // 批量保存多语言版本
          batchSaveMultiLanguage(dataToSave).then(response => {
            this.$modal.msgSuccess('保存成功');
            this.open = false;
            this.getList();
          }).catch(error => {
            this.$modal.msgError('保存失败：' + (error.msg || error.message));
          }).finally(() => {
            this.submitting = false;
          });
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal.confirm('注意删除分类，将同步删除此数据关联的其他语言分类，是否确认删除委员会成员编号为"' + ids + '"的数据项？').then(function () {
        return delArticle(ids)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess('删除成功')
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('yq/article/export', {
        ...this.queryParams
      }, `article_${new Date().getTime()}.xlsx`)
    },
    // 处理语言标签页点击
    handleLanguageTabClick(tab) {
      this.activeLanguageTab = tab.name
    },
    // 处理自动翻译
    handleAutoTranslate() {
      const sourceForm = this.multiLanguageForms[this.activeLanguageTab]
      if (!sourceForm || !sourceForm.title) {
        this.$modal.msgError('请先填写当前语言版本的标题')
        return
      }

      if (this.hasTranslated) {
        this.$modal.confirm('再次翻译将覆盖已有的翻译内容，是否确认继续？').then(() => {
          this.executeTranslation(sourceForm)
        }).catch(() => { })
      } else {
        this.executeTranslation(sourceForm)
      }
    },

    /** 执行翻译 */
    executeTranslation(sourceForm) {
      this.translating = true
      autoTranslateArticleData(sourceForm).then(response => {
        const translatedData = response.data || {}

        // 更新多语言表单数据
        Object.keys(translatedData).forEach(lang => {
          if (lang !== this.activeLanguageTab && this.multiLanguageForms[lang]) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang].id
            const originalRelationGroupId = this.multiLanguageForms[lang].relationGroupId

            // 更新表单数据，保留原有分类ID或使用翻译返回的分类ID
            const articleTypeId = translatedData[lang].articleTypeId !== undefined ?
              translatedData[lang].articleTypeId :
              this.multiLanguageForms[lang].articleTypeId;

            // 更新表单数据
            this.$set(this.multiLanguageForms, lang, {
              ...translatedData[lang],
              id: originalId,
              relationGroupId: originalRelationGroupId || this.currentRelationGroupId,
              articleTypeId: articleTypeId, // 使用翻译API返回的分类ID或保留原有的
              typeCode: 3
            })
          }
        })

        this.hasTranslated = true
        this.$modal.msgSuccess('翻译完成')
      }).catch(error => {
        this.$modal.msgError('翻译失败: ' + error)
      }).finally(() => {
        this.translating = false
      })
    },

    /** 清空其他语言 */
    handleClearOtherLanguages() {
      this.$modal.confirm('确定要清空其他语言的数据吗？').then(() => {
        const currentLanguageCode = this.activeLanguageTab

        // 清空其他语言的数据
        this.supportedLanguages.forEach(lang => {
          if (lang.value !== currentLanguageCode) {
            // 如果已有数据，则保留ID和关系组ID
            const originalId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].id : null
            const originalRelationGroupId = this.multiLanguageForms[lang.value] ? this.multiLanguageForms[lang.value].relationGroupId : this.currentRelationGroupId

            // 创建新的表单数据
            this.$set(this.multiLanguageForms, lang.value, {
              id: originalId,
              title: null,
              typeCode: 3,
              articleTypeId: null, // 不复制分类ID，各语言独立
              articleTypePath: null,
              articleTypeName: null,
              status: '0',
              orderNum: null,
              languageType: lang.value,
              relationGroupId: originalRelationGroupId,
              remark: null
            })
          }
        })

        this.$modal.msgSuccess('清空成功')
      }).catch(() => { })
    }
  }
}
</script>
